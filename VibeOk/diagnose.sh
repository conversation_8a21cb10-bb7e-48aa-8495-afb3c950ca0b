#!/bin/bash

echo "🔍 VibeOk Diagnostic Check"
echo "=========================="

# Check if in correct directory
if [ ! -f "package.json" ]; then
    echo "❌ Not in VibeOk directory"
    echo "💡 Navigate to the VibeOk folder first"
    exit 1
fi

echo "✅ In VibeOk directory"

# Check if config exists
if [ ! -f "src/constants/config.ts" ]; then
    echo "❌ Configuration file missing"
    echo "💡 Creating config file from template..."
    cp config-template.ts src/constants/config.ts
    echo "✅ Configuration file created"
else
    echo "✅ Configuration file exists"
fi

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "❌ Dependencies not installed"
    echo "💡 Run: npm install"
    exit 1
else
    echo "✅ Dependencies installed"
fi

# Check TypeScript compilation
echo "🔍 Checking TypeScript compilation..."
if npx tsc --noEmit > /dev/null 2>&1; then
    echo "✅ TypeScript compilation successful"
else
    echo "❌ TypeScript compilation errors"
    echo "💡 Running TypeScript check..."
    npx tsc --noEmit
    exit 1
fi

# Check if development server is running
if lsof -i :8081 > /dev/null 2>&1; then
    echo "⚠️  Port 8081 is already in use"
    echo "💡 Stop the existing server or run: npx kill-port 8081"
else
    echo "✅ Port 8081 is available"
fi

# Check network connectivity
echo "🔍 Checking network setup..."
LOCAL_IP=$(ifconfig | grep "inet " | grep -v 127.0.0.1 | awk '{print $2}' | head -1)
echo "📱 Your local IP: $LOCAL_IP"
echo "💡 Make sure your mobile device is on the same WiFi network"

echo ""
echo "🎉 Diagnostic complete!"
echo ""
echo "Next steps:"
echo "1. Run: npm start"
echo "2. Scan QR code with Expo Go app"
echo "3. Or open http://localhost:8081 in browser"
echo ""
echo "If you see a blank screen:"
echo "- Check browser console for errors (F12)"
echo "- Try refreshing the page"
echo "- Make sure you have internet connection"
