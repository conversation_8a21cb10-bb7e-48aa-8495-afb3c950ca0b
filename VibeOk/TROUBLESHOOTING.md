# VibeOk Troubleshooting Guide

## App Not Loading / Blank Screen Issues

### 1. Check Development Server Status

First, ensure the development server is running:

```bash
cd VibeOk
npm start
```

You should see:
- ✅ Metro bundler starting
- ✅ QR code displayed
- ✅ "Web is waiting on http://localhost:8081"

### 2. Configuration Issues

The most common cause of loading issues is missing configuration.

#### Check if config file exists:
```bash
ls -la src/constants/config.ts
```

If the file doesn't exist, create it:
```bash
cp config-template.ts src/constants/config.ts
```

#### Verify configuration values:
Open `src/constants/config.ts` and check:

```typescript
// These should NOT be the placeholder values:
export const SUPABASE_CONFIG = {
  url: 'YOUR_SUPABASE_URL', // ❌ Replace this
  anonKey: 'YOUR_SUPABASE_ANON_KEY', // ❌ Replace this
};

export const ZEGO_CONFIG = {
  appID: 0, // ❌ Replace this with actual number
  appSign: 'YOUR_ZEGO_APP_SIGN', // ❌ Replace this
};
```

### 3. Test with Minimal Configuration

If you don't have Supabase/ZEGOCLOUD credentials yet, you can test with mock values:

Create a temporary `src/constants/config.ts`:

```typescript
// Temporary configuration for testing
export const SUPABASE_CONFIG = {
  url: 'https://test.supabase.co',
  anonKey: 'test-key',
};

export const ZEGO_CONFIG = {
  appID: 123456789,
  appSign: 'test-sign',
};

export const APP_CONFIG = {
  name: 'VibeOk',
  version: '1.0.0',
  roomCodeLength: 8,
  maxParticipants: 10,
};

export const COLORS = {
  primary: '#6366f1',
  primaryDark: '#4f46e5',
  secondary: '#10b981',
  background: '#f8fafc',
  surface: '#ffffff',
  text: '#1f2937',
  textSecondary: '#6b7280',
  error: '#ef4444',
  warning: '#f59e0b',
  success: '#10b981',
  border: '#e5e7eb',
};

export const SPACING = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

export const FONT_SIZES = {
  xs: 12,
  sm: 14,
  md: 16,
  lg: 18,
  xl: 20,
  xxl: 24,
  xxxl: 32,
};
```

### 4. Check Browser Console (Web Version)

If testing on web (http://localhost:8081):

1. Open browser developer tools (F12)
2. Check Console tab for errors
3. Look for red error messages

Common errors and solutions:

#### "Cannot resolve module"
- **Cause**: Missing dependencies or incorrect imports
- **Solution**: Run `npm install` again

#### "Network Error" or "Failed to fetch"
- **Cause**: Cannot connect to Supabase
- **Solution**: Check internet connection or use mock config

#### "ZEGOCLOUD configuration error"
- **Cause**: Invalid ZEGOCLOUD credentials
- **Solution**: Use mock values for testing

### 5. Test on Mobile Device

For the best experience, test on a real mobile device:

1. Install **Expo Go** app from App Store/Play Store
2. Ensure your phone and computer are on the same WiFi network
3. Scan the QR code from the terminal
4. Wait for the app to load

### 6. Check Metro Bundler Logs

Look at the terminal where you ran `npm start` for error messages:

#### Common Metro Errors:

**"Unable to resolve module"**
```bash
# Solution: Clear cache and reinstall
npm start -- --clear
```

**"Port 8081 already in use"**
```bash
# Solution: Kill existing process
npx kill-port 8081
npm start
```

**"Watchman error"**
```bash
# Solution: Reset watchman
watchman watch-del-all
npm start
```

### 7. Platform-Specific Issues

#### iOS Simulator
```bash
npm run ios
```

#### Android Emulator
```bash
npm run android
```

#### Web Browser
```bash
npm run web
```

### 8. Dependency Issues

If you see package version warnings:

```bash
# Update to compatible versions
npx expo install --fix
```

### 9. Clear Cache and Restart

If nothing else works:

```bash
# Clear npm cache
npm start -- --clear

# Or clear everything
rm -rf node_modules
npm install
npm start
```

### 10. Check Network Connectivity

Ensure your device can reach the development server:

1. Note the IP address from the QR code (e.g., `*************:8081`)
2. Try opening `http://[IP]:8081` in your phone's browser
3. You should see the Expo development page

## Quick Diagnostic Script

Run this to check your setup:

```bash
#!/bin/bash
echo "🔍 VibeOk Diagnostic Check"
echo "=========================="

# Check if in correct directory
if [ ! -f "package.json" ]; then
    echo "❌ Not in VibeOk directory"
    exit 1
fi

echo "✅ In VibeOk directory"

# Check if config exists
if [ ! -f "src/constants/config.ts" ]; then
    echo "❌ Configuration file missing"
    echo "💡 Run: cp config-template.ts src/constants/config.ts"
else
    echo "✅ Configuration file exists"
fi

# Check if node_modules exists
if [ ! -d "node_modules" ]; then
    echo "❌ Dependencies not installed"
    echo "💡 Run: npm install"
else
    echo "✅ Dependencies installed"
fi

# Check TypeScript compilation
if npx tsc --noEmit > /dev/null 2>&1; then
    echo "✅ TypeScript compilation successful"
else
    echo "❌ TypeScript compilation errors"
    echo "💡 Run: npx tsc --noEmit"
fi

echo ""
echo "🚀 If all checks pass, run: npm start"
```

## Getting Help

If you're still having issues:

1. **Check the exact error message** in the terminal or browser console
2. **Try the minimal configuration** approach above
3. **Test on different platforms** (web, iOS, Android)
4. **Ensure network connectivity** between devices

## Common Solutions Summary

| Issue | Solution |
|-------|----------|
| Blank screen | Check config file exists |
| "Cannot resolve module" | Run `npm install` |
| Network errors | Use mock configuration |
| Metro bundler issues | Run `npm start -- --clear` |
| Port conflicts | Run `npx kill-port 8081` |
| Dependency warnings | Run `npx expo install --fix` |

The app should load with the login screen once configuration issues are resolved!
