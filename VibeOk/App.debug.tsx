import React, { useEffect } from 'react';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import { View, Text, StyleSheet } from 'react-native';

import { AuthProvider } from './src/contexts/AuthContext';
import { MeetingProvider } from './src/contexts/MeetingContext';
import AppNavigator from './src/navigation/AppNavigator';

// Debug component to test basic rendering
function DebugWrapper({ children }: { children: React.ReactNode }) {
  useEffect(() => {
    console.log('🚀 VibeOk App: DebugWrapper mounted');
    console.log('📱 Platform:', require('react-native').Platform.OS);
    console.log('🔧 Environment variables:', {
      SUPABASE_URL: process.env.EXPO_PUBLIC_SUPABASE_URL ? '✅ Set' : '❌ Missing',
      SUPABASE_KEY: process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY ? '✅ Set' : '❌ Missing',
      ZEGO_APP_ID: process.env.EXPO_PUBLIC_ZEGO_APP_ID ? '✅ Set' : '❌ Missing',
      ZEGO_APP_SIGN: process.env.EXPO_PUBLIC_ZEGO_APP_SIGN ? '✅ Set' : '❌ Missing',
    });
  }, []);

  return (
    <View style={styles.debugContainer}>
      {children}
    </View>
  );
}

export default function App() {
  console.log('🎯 VibeOk App: Starting render');

  return (
    <SafeAreaProvider>
      <DebugWrapper>
        <AuthProvider>
          <MeetingProvider>
            <AppNavigator />
            <StatusBar style="auto" />
          </MeetingProvider>
        </AuthProvider>
      </DebugWrapper>
    </SafeAreaProvider>
  );
}

const styles = StyleSheet.create({
  debugContainer: {
    flex: 1,
  },
});
