import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { StatusBar } from 'expo-status-bar';

// Test imports one by one to see where it fails
let SUPABASE_CONFIG: any;
let ZEGO_CONFIG: any;
let supabase: any;

try {
  const config = require('./src/constants/config');
  SUPABASE_CONFIG = config.SUPABASE_CONFIG;
  ZEGO_CONFIG = config.ZEGO_CONFIG;
  console.log('✅ Config loaded successfully');
} catch (error) {
  console.error('❌ Config loading failed:', error);
}

try {
  const supabaseService = require('./src/services/supabase');
  supabase = supabaseService.supabase;
  console.log('✅ Supabase service loaded successfully');
} catch (error) {
  console.error('❌ Supabase service loading failed:', error);
}

export default function App() {
  const [debugInfo, setDebugInfo] = useState<string[]>([]);

  useEffect(() => {
    const info: string[] = [];
    
    // Check environment variables
    info.push('=== Environment Variables ===');
    info.push(`EXPO_PUBLIC_SUPABASE_URL: ${process.env.EXPO_PUBLIC_SUPABASE_URL ? '✅ Set' : '❌ Missing'}`);
    info.push(`EXPO_PUBLIC_SUPABASE_ANON_KEY: ${process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY ? '✅ Set' : '❌ Missing'}`);
    info.push(`EXPO_PUBLIC_ZEGO_APP_ID: ${process.env.EXPO_PUBLIC_ZEGO_APP_ID ? '✅ Set' : '❌ Missing'}`);
    info.push(`EXPO_PUBLIC_ZEGO_APP_SIGN: ${process.env.EXPO_PUBLIC_ZEGO_APP_SIGN ? '✅ Set' : '❌ Missing'}`);
    
    // Check config values
    info.push('\n=== Config Values ===');
    if (SUPABASE_CONFIG) {
      info.push(`Supabase URL: ${SUPABASE_CONFIG.url}`);
      info.push(`Supabase Key: ${SUPABASE_CONFIG.anonKey ? SUPABASE_CONFIG.anonKey.substring(0, 20) + '...' : 'Missing'}`);
    } else {
      info.push('❌ SUPABASE_CONFIG not loaded');
    }
    
    if (ZEGO_CONFIG) {
      info.push(`ZEGO App ID: ${ZEGO_CONFIG.appID}`);
      info.push(`ZEGO App Sign: ${ZEGO_CONFIG.appSign ? ZEGO_CONFIG.appSign.substring(0, 20) + '...' : 'Missing'}`);
    } else {
      info.push('❌ ZEGO_CONFIG not loaded');
    }
    
    // Test Supabase connection
    info.push('\n=== Supabase Connection Test ===');
    if (supabase) {
      supabase.auth.getSession()
        .then(() => {
          info.push('✅ Supabase connection successful');
          setDebugInfo([...info]);
        })
        .catch((error: any) => {
          info.push(`❌ Supabase connection failed: ${error.message}`);
          setDebugInfo([...info]);
        });
    } else {
      info.push('❌ Supabase client not initialized');
    }
    
    setDebugInfo(info);
  }, []);

  return (
    <View style={styles.container}>
      <StatusBar style="auto" />
      
      <Text style={styles.title}>🔍 VibeOk Debug Mode</Text>
      
      <ScrollView style={styles.scrollView} contentContainerStyle={styles.scrollContent}>
        {debugInfo.map((line, index) => (
          <Text 
            key={index} 
            style={[
              styles.debugText,
              line.includes('❌') && styles.errorText,
              line.includes('✅') && styles.successText,
              line.includes('===') && styles.headerText,
            ]}
          >
            {line}
          </Text>
        ))}
      </ScrollView>
      
      <Text style={styles.footer}>
        If you see this screen, React Native is working!
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
    padding: 20,
    paddingTop: 50,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#6366f1',
    textAlign: 'center',
    marginBottom: 20,
  },
  scrollView: {
    flex: 1,
    backgroundColor: '#ffffff',
    borderRadius: 8,
    padding: 16,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  debugText: {
    fontSize: 12,
    color: '#374151',
    marginBottom: 4,
    fontFamily: 'monospace',
  },
  errorText: {
    color: '#ef4444',
    fontWeight: '600',
  },
  successText: {
    color: '#10b981',
    fontWeight: '600',
  },
  headerText: {
    color: '#6366f1',
    fontWeight: 'bold',
    fontSize: 14,
    marginTop: 8,
    marginBottom: 4,
  },
  footer: {
    fontSize: 14,
    color: '#6b7280',
    textAlign: 'center',
    marginTop: 16,
  },
});
