import React from 'react';
import { Platform } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';

// Import contexts that should work on all platforms
import { AuthProvider } from './src/contexts/AuthContext';
import { MeetingProvider } from './src/contexts/MeetingContext';

// Platform-specific imports
let AppNavigator: any;

if (Platform.OS === 'web') {
  // For web, use a simplified navigator without ZEGOCLOUD
  AppNavigator = require('./src/navigation/AppNavigator.web').default;
} else {
  // For mobile, use the full navigator with ZEGOCLOUD
  AppNavigator = require('./src/navigation/AppNavigator').default;
}

export default function App() {
  console.log('🎯 VibeOk App: Starting on platform:', Platform.OS);
  
  return (
    <SafeAreaProvider>
      <AuthProvider>
        <MeetingProvider>
          <AppNavigator />
          <StatusBar style="auto" />
        </MeetingProvider>
      </AuthProvider>
    </SafeAreaProvider>
  );
}
