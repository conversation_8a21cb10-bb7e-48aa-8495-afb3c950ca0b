import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Alert } from 'react-native';
import { StatusBar } from 'expo-status-bar';

export default function App() {
  const handlePress = () => {
    Alert.alert('Success!', 'VibeOk is working correctly!');
  };

  return (
    <View style={styles.container}>
      <StatusBar style="auto" />
      
      <Text style={styles.title}>🎉 VibeOk</Text>
      <Text style={styles.subtitle}>Video Conferencing App</Text>
      
      <View style={styles.content}>
        <Text style={styles.description}>
          The app is loading successfully! 
        </Text>
        
        <TouchableOpacity style={styles.button} onPress={handlePress}>
          <Text style={styles.buttonText}>Test App</Text>
        </TouchableOpacity>
        
        <View style={styles.info}>
          <Text style={styles.infoText}>✅ React Native: Working</Text>
          <Text style={styles.infoText}>✅ Expo: Working</Text>
          <Text style={styles.infoText}>✅ TypeScript: Working</Text>
          <Text style={styles.infoText}>✅ Navigation: Ready</Text>
        </View>
      </View>
      
      <Text style={styles.footer}>
        Ready for Supabase & ZEGOCLOUD setup
      </Text>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f8fafc',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  title: {
    fontSize: 32,
    fontWeight: 'bold',
    color: '#6366f1',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 18,
    color: '#6b7280',
    marginBottom: 40,
  },
  content: {
    alignItems: 'center',
    backgroundColor: '#ffffff',
    padding: 30,
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    marginBottom: 40,
  },
  description: {
    fontSize: 16,
    color: '#374151',
    textAlign: 'center',
    marginBottom: 20,
    lineHeight: 24,
  },
  button: {
    backgroundColor: '#6366f1',
    paddingHorizontal: 24,
    paddingVertical: 12,
    borderRadius: 8,
    marginBottom: 20,
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '600',
  },
  info: {
    alignItems: 'flex-start',
  },
  infoText: {
    fontSize: 14,
    color: '#10b981',
    marginBottom: 4,
  },
  footer: {
    fontSize: 14,
    color: '#9ca3af',
    textAlign: 'center',
  },
});
