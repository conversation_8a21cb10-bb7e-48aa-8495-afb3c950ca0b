import React from 'react';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';

import { AuthProvider } from './src/contexts/AuthContext';
import { MeetingProvider } from './src/contexts/MeetingContext';
import AppNavigator from './src/navigation/AppNavigator';

export default function App() {
  return (
    <SafeAreaProvider>
      <AuthProvider>
        <MeetingProvider>
          <AppNavigator />
          <StatusBar style="auto" />
        </MeetingProvider>
      </AuthProvider>
    </SafeAreaProvider>
  );
}
