{"name": "vibeok", "version": "1.0.0", "main": "index.ts", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web"}, "dependencies": {"@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-navigation/bottom-tabs": "^7.3.14", "@react-navigation/native": "^7.1.10", "@react-navigation/stack": "^7.3.3", "@supabase/supabase-js": "^2.50.0", "@zegocloud/zego-uikit-prebuilt-call-rn": "^6.3.11", "@zegocloud/zego-uikit-prebuilt-video-conference-rn": "^1.1.0", "@zegocloud/zego-uikit-rn": "^2.17.9", "expo": "~53.0.10", "expo-status-bar": "~2.2.3", "react": "19.0.0", "react-delegate-component": "^1.0.0", "react-dom": "19.0.0", "react-native": "0.79.3", "react-native-paper": "^5.14.5", "react-native-safe-area-context": "5.4.0", "react-native-screens": "^4.11.1", "react-native-url-polyfill": "^2.0.0", "react-native-uuid": "^2.0.3", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.20.0", "zego-express-engine-reactnative": "^3.20.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "typescript": "~5.8.3"}, "private": true}