{"name": "@zegocloud/zego-uikit-prebuilt-video-conference-rn", "version": "1.1.0", "description": "PrebuiltVideoConference is a full-featured Video Conference kit that provides a ready-made group voice/video chat, in-room message, dynamic layout, member list, etc.", "main": "lib/commonjs/index", "module": "lib/module/index", "files": ["lib", "android", "ios", "zego-uikit-prebuilt-video-conference-rn.podspec", "!lib/typescript/example", "!android/build", "!ios/build", "!**/__tests__", "!**/__fixtures__", "!**/__mocks__"], "scripts": {"test": "jest", "typescript": "tsc --noEmit", "lint": "eslint \"**/*.{js,ts,tsx}\"", "release": "release-it", "example": "yarn --cwd example", "pods": "cd example && RCT_NEW_ARCH_ENABLED=0 pod-install --quiet", "bootstrap": "yarn example && yarn && yarn pods"}, "keywords": ["react-native", "ios", "android"], "repository": "https://github.com/ZEGOCLOUD/zego_uikit_prebuilt_video_conference_rn", "author": "<PERSON> <<EMAIL>> (https://zegocloud.com)", "license": "MIT", "bugs": {"url": "https://github.com/ZEGOCLOUD/zego_uikit_prebuilt_video_conference_rn/issues"}, "homepage": "https://zegocloud.com", "publishConfig": {"registry": "https://registry.npmjs.org/"}, "devDependencies": {"@arkweid/lefthook": "^0.7.7", "@babel/eslint-parser": "^7.18.2", "@react-native-community/eslint-config": "^3.0.2", "@release-it/conventional-changelog": "^5.0.0", "@types/jest": "^28.1.2", "@types/react": "~17.0.21", "@types/react-native": "0.68.0", "babel-preset-minify": "^0.5.2", "eslint": "^8.4.1", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^28.1.1", "pod-install": "^0.1.0", "prettier": "^2.0.5", "react": "17.0.2", "react-native": "0.68.2", "redux": "^4.2.0", "release-it": "^15.0.0", "typescript": "^4.5.2"}, "resolutions": {"@types/react": "17.0.21"}, "peerDependencies": {"react": "*", "react-delegate-component": "*", "react-native": "*", "@zegocloud/zego-uikit-rn": ">=1.12.0"}, "jest": {"preset": "react-native", "modulePathIgnorePatterns": ["<rootDir>/example/node_modules", "<rootDir>/lib/"]}, "commitlint": {"extends": ["@commitlint/config-conventional"]}, "release-it": {"git": {"commitMessage": "chore: release ${version}", "tagName": "v${version}"}, "npm": {"publish": true}, "github": {"release": true}, "plugins": {"@release-it/conventional-changelog": {"preset": "angular"}}}, "eslintConfig": {"root": true, "parser": "@babel/eslint-parser", "extends": ["@react-native-community", "prettier"], "rules": {"prettier/prettier": ["error", {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}]}}, "eslintIgnore": ["node_modules/", "lib/"], "prettier": {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}}