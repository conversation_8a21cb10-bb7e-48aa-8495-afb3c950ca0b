{"version": 3, "names": ["React", "useEffect", "View", "Text", "StyleSheet", "ZegoMicrophoneStateIcon", "ZegoCameraStateIcon", "AudioVideoForegroundView", "props", "userInfo", "showUserNameOnView", "showCameraStateOnView", "showMicrophoneStateOnView", "userID", "userName", "styles", "foregroundViewContainer", "bottomContainer", "nameLabelContainer", "name<PERSON><PERSON><PERSON>", "deviceIcon", "create", "flex", "position", "width", "height", "zIndex", "flexDirection", "justifyContent", "backgroundColor", "opacity", "alignSelf", "paddingLeft", "paddingRight", "paddingBottom", "paddingTop", "borderRadius", "bottom", "right", "color", "fontSize"], "sources": ["AudioVideoForegroundView.js"], "sourcesContent": ["import React, { useEffect } from \"react\";\nimport { View, Text, StyleSheet } from \"react-native\"\nimport { ZegoMicrophoneStateIcon, ZegoCameraStateIcon } from '@zegocloud/zego-uikit-rn'\n\nexport default function AudioVideoForegroundView(props) {\n    const { userInfo, showUserNameOnView, showCameraStateOnView, showMicrophoneStateOnView } = props;\n    const { userID = '', userName = '' } = userInfo;\n\n    return (\n        <View style={styles.foregroundViewContainer}>\n            <View style={styles.bottomContainer}>\n                {showUserNameOnView ?\n                    <View style={styles.nameLabelContainer}>\n                        <Text style={styles.nameLabel}>{userName}</Text>\n                    </View> :\n                    <View />\n                }\n                {showCameraStateOnView ? <ZegoCameraStateIcon userID={userID} style={styles.deviceIcon} /> : <View />}\n                {showMicrophoneStateOnView ? <ZegoMicrophoneStateIcon userID={userID} style={styles.deviceIcon} /> : <View />}\n            </View>\n        </View>\n    );\n}\n\n\nconst styles = StyleSheet.create({\n    foregroundViewContainer: {\n        flex: 1,\n        position: 'absolute',\n        width: '100%',\n        height: '100%',\n        zIndex: 10,\n    },\n    bottomContainer: {\n        flex: 1,\n        flexDirection: 'row',\n        justifyContent: 'flex-end',\n        backgroundColor: '#2A2A2A',\n        opacity: 0.5,\n        position: 'absolute',\n        alignSelf: 'center',\n        paddingLeft: 5,\n        paddingRight: 5,\n        paddingBottom: 3,\n        paddingTop: 3,\n        borderRadius: 6,\n        bottom: 5,\n        right: 5\n    },\n    nameLabelContainer: {\n        alignSelf: 'center',\n    },\n    nameLabel: {\n        color: '#FFFFFF',\n        fontSize: 12,\n    },\n    deviceIcon: {\n        flex: 1,\n        position: 'absolute'\n    }\n});\n"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,SAAS,QAAQ,OAAO;AACxC,SAASC,IAAI,EAAEC,IAAI,EAAEC,UAAU,QAAQ,cAAc;AACrD,SAASC,uBAAuB,EAAEC,mBAAmB,QAAQ,0BAA0B;AAEvF,eAAe,SAASC,wBAAwB,CAACC,KAAK,EAAE;EACpD,MAAM;IAAEC,QAAQ;IAAEC,kBAAkB;IAAEC,qBAAqB;IAAEC;EAA0B,CAAC,GAAGJ,KAAK;EAChG,MAAM;IAAEK,MAAM,GAAG,EAAE;IAAEC,QAAQ,GAAG;EAAG,CAAC,GAAGL,QAAQ;EAE/C,oBACI,oBAAC,IAAI;IAAC,KAAK,EAAEM,MAAM,CAACC;EAAwB,gBACxC,oBAAC,IAAI;IAAC,KAAK,EAAED,MAAM,CAACE;EAAgB,GAC/BP,kBAAkB,gBACf,oBAAC,IAAI;IAAC,KAAK,EAAEK,MAAM,CAACG;EAAmB,gBACnC,oBAAC,IAAI;IAAC,KAAK,EAAEH,MAAM,CAACI;EAAU,GAAEL,QAAQ,CAAQ,CAC7C,gBACP,oBAAC,IAAI,OAAG,EAEXH,qBAAqB,gBAAG,oBAAC,mBAAmB;IAAC,MAAM,EAAEE,MAAO;IAAC,KAAK,EAAEE,MAAM,CAACK;EAAW,EAAG,gBAAG,oBAAC,IAAI,OAAG,EACpGR,yBAAyB,gBAAG,oBAAC,uBAAuB;IAAC,MAAM,EAAEC,MAAO;IAAC,KAAK,EAAEE,MAAM,CAACK;EAAW,EAAG,gBAAG,oBAAC,IAAI,OAAG,CAC1G,CACJ;AAEf;AAGA,MAAML,MAAM,GAAGX,UAAU,CAACiB,MAAM,CAAC;EAC7BL,uBAAuB,EAAE;IACrBM,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE,UAAU;IACpBC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdC,MAAM,EAAE;EACZ,CAAC;EACDT,eAAe,EAAE;IACbK,IAAI,EAAE,CAAC;IACPK,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,UAAU;IAC1BC,eAAe,EAAE,SAAS;IAC1BC,OAAO,EAAE,GAAG;IACZP,QAAQ,EAAE,UAAU;IACpBQ,SAAS,EAAE,QAAQ;IACnBC,WAAW,EAAE,CAAC;IACdC,YAAY,EAAE,CAAC;IACfC,aAAa,EAAE,CAAC;IAChBC,UAAU,EAAE,CAAC;IACbC,YAAY,EAAE,CAAC;IACfC,MAAM,EAAE,CAAC;IACTC,KAAK,EAAE;EACX,CAAC;EACDpB,kBAAkB,EAAE;IAChBa,SAAS,EAAE;EACf,CAAC;EACDZ,SAAS,EAAE;IACPoB,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE;EACd,CAAC;EACDpB,UAAU,EAAE;IACRE,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACd;AACJ,CAAC,CAAC"}