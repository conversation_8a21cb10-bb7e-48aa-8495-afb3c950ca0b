{"version": 3, "names": ["React", "useEffect", "useState", "useRef", "PermissionsAndroid", "<PERSON><PERSON>", "StyleSheet", "View", "ZegoUIKit", "ZegoAudioVideoContainer", "ZegoLayoutMode", "AudioVideoForegroundView", "ZegoBottomBar", "ZegoTopMenuBar", "ZegoCallMemberList", "ZegoMenuBarButtonName", "ZegoMenuBarStyle", "ZegoUIKitPrebuiltVideoConference", "props", "appID", "appSign", "userID", "userName", "conferenceID", "config", "token", "onRequireNewToken", "audioVideoViewConfig", "turnOnCameraWhenJoining", "turnOnMicrophoneWhenJoining", "useSpeakerWhenJoining", "bottomMenuBarConfig", "topMenuBarConfig", "memberListConfig", "layout", "mode", "gallery", "addBorderRadiusAndSpacingBetweenView", "leaveConfirmDialogInfo", "onLeave", "onLeaveConfirmation", "showMicrophoneStateOnView", "showCameraStateOnView", "showUserNameOnView", "showSoundWavesInAudioMode", "useVideoViewAspectFill", "foregroundBuilder", "buttons", "toggleCameraButton", "switchCameraButton", "leaveButton", "toggleMicrophoneButton", "switchAudioOutputButton", "maxCount", "extendButtons", "hideAutomatically", "hideByClick", "style", "dark", "isVisible", "title", "topTitle", "topButtons", "showMemberListButton", "topMaxCount", "topExtendButtons", "topHideAutomatically", "topHideByClick", "topStyle", "showMicrophoneState", "showCameraState", "itemBuilder", "isMenubarVisable", "setIsMenubarVidable", "isTopMenubarVisable", "setTopIsMenubarVidable", "isCallMemberListVisable", "setIsCallMemberListVisable", "hideCountdown", "hideCountdownOnTopMenu", "onFullPageTouch", "grantPermissions", "callback", "Platform", "OS", "grantedAudio", "check", "PERMISSIONS", "RECORD_AUDIO", "grantedCamera", "CAMERA", "ungrantedPermissions", "isAudioGranted", "isVideoGranted", "push", "error", "requestMultiple", "then", "data", "console", "warn", "showLeaveAlert", "Promise", "resolve", "reject", "message", "cancelButtonName", "confirmButtonName", "alert", "text", "onPress", "cancelable", "callbackID", "String", "Math", "floor", "random", "init", "turnCameraOn", "turnMicrophoneOn", "setAudioOutputToSpeaker", "joinRoom", "leaveRoom", "useInterval", "delay", "savedCallback", "current", "tick", "id", "setInterval", "clearInterval", "onOpenCallMemberList", "onCloseCallMemberList", "styles", "container", "fillParent", "audioVideoView", "userInfo", "create", "flex", "alignItems", "justifyContent", "zIndex", "width", "height", "position", "right", "top"], "sources": ["index.js"], "sourcesContent": ["import React, { useEffect, useState, useRef } from 'react';\nimport { PermissionsAndroid, Alert } from 'react-native';\n\nimport { StyleSheet, View } from 'react-native';\nimport ZegoUIKit, { ZegoAudioVideoContainer, ZegoLayoutMode } from '@zegocloud/zego-uikit-rn'\nimport AudioVideoForegroundView from './AudioVideoForegroundView';\nimport ZegoBottomBar from './ZegoBottomBar';\nimport ZegoTopMenuBar from './ZegoTopMenuBar';\nimport ZegoCallMemberList from './ZegoCallMemberList';\nimport ZegoMenuBarButtonName from './ZegoMenuBarButtonName';\nimport ZegoMenuBarStyle from './ZegoMenuBarStyle';\n\n\nexport default function ZegoUIKitPrebuiltVideoConference(props) {\n    const {\n        appID,\n        appSign,\n        userID,\n        userName,\n        conferenceID,\n        config,\n        token,\n        onRequireNewToken,\n    } = props;\n    const {\n        audioVideoViewConfig = {},\n\n        turnOnCameraWhenJoining = true,\n        turnOnMicrophoneWhenJoining = true,\n        useSpeakerWhenJoining = true,\n\n        bottomMenuBarConfig = {},\n        topMenuBarConfig = {},\n        memberListConfig = {},\n\n        layout = { \n            mode: ZegoLayoutMode.gallery,\n            config: {\n                addBorderRadiusAndSpacingBetweenView: false\n            }\n        },\n\n        leaveConfirmDialogInfo, // {title: '', cancelButtonName: '', confirmButtonName: ''}\n\n        onLeave,\n        onLeaveConfirmation,\n    } = config;\n    const {\n        showMicrophoneStateOnView = true,\n        showCameraStateOnView = false,\n        showUserNameOnView = true,\n        showSoundWavesInAudioMode = true,\n        useVideoViewAspectFill = false,\n        foregroundBuilder,\n    } = audioVideoViewConfig;\n    const {\n        buttons = [\n            ZegoMenuBarButtonName.toggleCameraButton,\n            ZegoMenuBarButtonName.switchCameraButton,\n            ZegoMenuBarButtonName.leaveButton,\n            ZegoMenuBarButtonName.toggleMicrophoneButton,\n            ZegoMenuBarButtonName.switchAudioOutputButton\n        ],\n        maxCount = 5,\n        extendButtons = [],\n        hideAutomatically = true,\n        hideByClick = true,\n        style = ZegoMenuBarStyle.dark,\n    } = bottomMenuBarConfig;\n    const {\n        isVisible = true,\n        title:topTitle = 'Meeting',\n        buttons:topButtons = [ ZegoMenuBarButtonName.showMemberListButton ],\n        maxCount:topMaxCount = 3,\n        extendButtons:topExtendButtons = [],\n        hideAutomatically:topHideAutomatically = true,\n        hideByClick:topHideByClick = true,\n        style:topStyle = ZegoMenuBarStyle.dark,\n    } = topMenuBarConfig;\n    const {\n        showMicrophoneState = true,\n        showCameraState = true,\n        itemBuilder,\n    } = memberListConfig;\n\n    const [isMenubarVisable, setIsMenubarVidable] = useState(true);\n    const [isTopMenubarVisable, setTopIsMenubarVidable] = useState(true);\n    const [isCallMemberListVisable, setIsCallMemberListVisable] = useState(false);\n    var hideCountdown = 5;\n    var hideCountdownOnTopMenu = 5;\n\n    const onFullPageTouch = () => {\n        hideCountdown = 5;\n        hideCountdownOnTopMenu = 5;\n        if (isMenubarVisable) {\n            if (hideByClick) {\n                setIsMenubarVidable(false);\n                setIsCallMemberListVisable(false);\n            }\n        } else {\n            setIsMenubarVidable(true);\n        }\n        if (isTopMenubarVisable) {\n            if (topHideByClick) {\n                setTopIsMenubarVidable(false);\n                setIsCallMemberListVisable(false);\n            }\n        } else {\n            setTopIsMenubarVidable(true);\n        }\n    }\n    const grantPermissions = async (callback) => {\n        // Android: Dynamically obtaining device permissions\n        if (Platform.OS === 'android') {\n            // Check if permission granted\n            let grantedAudio = PermissionsAndroid.check(\n                PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,\n            );\n            let grantedCamera = PermissionsAndroid.check(\n                PermissionsAndroid.PERMISSIONS.CAMERA,\n            );\n            const ungrantedPermissions = [];\n            try {\n                const isAudioGranted = await grantedAudio;\n                const isVideoGranted = await grantedCamera;\n                if (!isAudioGranted) {\n                    ungrantedPermissions.push(\n                        PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,\n                    );\n                }\n                if (!isVideoGranted) {\n                    ungrantedPermissions.push(PermissionsAndroid.PERMISSIONS.CAMERA);\n                }\n            } catch (error) {\n                ungrantedPermissions.push(\n                    PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,\n                    PermissionsAndroid.PERMISSIONS.CAMERA,\n                );\n            }\n            // If not, request it\n            return PermissionsAndroid.requestMultiple(ungrantedPermissions).then(\n                data => {\n                    console.warn('requestMultiple', data);\n                    if (callback) {\n                        callback();\n                    }\n                },\n            );\n        } else if (callback) {\n            callback();\n        }\n    }\n    // Default operation for click the leave button\n    const showLeaveAlert = () => {\n        return new Promise((resolve, reject) => {\n            if (leaveConfirmDialogInfo) {\n                const {\n                    title = \"Leave the call\",\n                    message = \"Are you sure to leave the call?\",\n                    cancelButtonName = \"Cancel\",\n                    confirmButtonName = \"Confirm\"\n                } = leaveConfirmDialogInfo;\n                Alert.alert(\n                    title,\n                    message,\n                    [\n                        {\n                            text: cancelButtonName,\n                            onPress: () => {\n                                reject();\n                            },\n                            style: \"cancel\",\n                        },\n                        {\n                            text: confirmButtonName,\n                            onPress: () => {\n                                resolve();\n                            },\n                        },\n                    ],\n                    {\n                        cancelable: false,\n                    }\n                );\n            } else {\n                resolve();\n            }\n        });\n    }\n\n    useEffect(() => {\n        const callbackID = 'ZegoUIKitPrebuiltVideoConference' + String(Math.floor(Math.random() * 10000));\n        ZegoUIKit.onRequireNewToken(callbackID, onRequireNewToken);\n        return () => {\n            ZegoUIKit.onRequireNewToken(callbackID);\n        }\n    }, []);\n    useEffect(() => {\n        ZegoUIKit.init(\n            appID,\n            appSign,\n            { userID: userID, userName: userName }).then(() => {\n\n                ZegoUIKit.turnCameraOn('', turnOnCameraWhenJoining);\n                ZegoUIKit.turnMicrophoneOn('', turnOnMicrophoneWhenJoining);\n                ZegoUIKit.setAudioOutputToSpeaker(useSpeakerWhenJoining);\n\n                grantPermissions(() => {\n                    if (appSign) {\n                        ZegoUIKit.joinRoom(conferenceID);\n                    } else {\n                        ZegoUIKit.joinRoom(conferenceID, token || onRequireNewToken());\n                    }\n                });\n\n            });\n\n        return () => {\n            ZegoUIKit.leaveRoom();\n        }\n    }, []);\n\n    function useInterval(callback, delay) {\n        const savedCallback = useRef();\n\n        useEffect(() => {\n            savedCallback.current = callback;\n        });\n\n        useEffect(() => {\n            function tick() {\n                savedCallback.current();\n            }\n            if (delay !== null) {\n                let id = setInterval(tick, delay);\n                return () => clearInterval(id);\n            }\n        }, [delay]);\n    }\n\n    function onOpenCallMemberList() {\n        setIsCallMemberListVisable(true);\n    }\n\n    function onCloseCallMemberList() {\n        setIsCallMemberListVisable(false);\n    }\n\n    useInterval(() => {\n        hideCountdown--;\n        if (hideCountdown <= 0) {\n            hideCountdown = 5;\n            if (hideAutomatically) {\n                setIsMenubarVidable(false);\n            }\n        }\n    }, 1000);\n\n    useInterval(() => {\n        hideCountdownOnTopMenu--;\n        if (hideCountdownOnTopMenu <= 0) {\n            hideCountdownOnTopMenu = 5;\n            if (topHideAutomatically) {\n                setTopIsMenubarVidable(false);\n            }\n        }\n    }, 1000);\n\n    return (\n        <View style={[styles.container, styles.fillParent]} >\n            {isVisible && isTopMenubarVisable ?\n                <ZegoTopMenuBar\n                    menuTitle={topTitle}\n                    menuBarButtonsMaxCount={topMaxCount}\n                    menuBarButtons={topButtons}\n                    menuBarExtendedButtons={topExtendButtons}\n                    onLeave={onLeave}\n                    onLeaveConfirmation={onLeaveConfirmation ? onLeaveConfirmation : showLeaveAlert}\n                    turnOnCameraWhenJoining={turnOnCameraWhenJoining}\n                    turnOnMicrophoneWhenJoining={turnOnMicrophoneWhenJoining}\n                    useSpeakerWhenJoining={useSpeakerWhenJoining}\n                    onOpenCallMemberList={onOpenCallMemberList}\n                /> : <View />\n            }\n            <View style={styles.fillParent} pointerEvents='auto' onTouchStart={onFullPageTouch}>\n                <ZegoAudioVideoContainer style={[styles.audioVideoView, styles.fillParent]}\n                    audioVideoConfig={{\n                        showSoundWavesInAudioMode: showSoundWavesInAudioMode,\n                        useVideoViewAspectFill: useVideoViewAspectFill,\n                    }}\n                    layout={layout}\n                    foregroundBuilder={foregroundBuilder ? foregroundBuilder : ({ userInfo }) =>\n                        <AudioVideoForegroundView\n                            userInfo={userInfo}\n                            showMicrophoneStateOnView={showMicrophoneStateOnView}\n                            showCameraStateOnView={showCameraStateOnView}\n                            showUserNameOnView={showUserNameOnView}\n                        />\n                    }\n                />\n            </View>\n            {isMenubarVisable ?\n                <ZegoBottomBar\n                    menuBarButtonsMaxCount={maxCount}\n                    menuBarButtons={buttons}\n                    menuBarExtendedButtons={extendButtons}\n                    onLeave={onLeave}\n                    onLeaveConfirmation={onLeaveConfirmation ? onLeaveConfirmation : showLeaveAlert}\n                    turnOnCameraWhenJoining={turnOnCameraWhenJoining}\n                    turnOnMicrophoneWhenJoining={turnOnMicrophoneWhenJoining}\n                    useSpeakerWhenJoining={useSpeakerWhenJoining}\n                    onMorePress={() => { hideCountdown = 5; }}\n                /> :\n                <View />\n            }\n            {isCallMemberListVisable ?\n                <ZegoCallMemberList\n                    showMicrophoneState={showMicrophoneState}\n                    showCameraState={showCameraState}\n                    itemBuilder={itemBuilder}\n                    onCloseCallMemberList={onCloseCallMemberList}\n                /> :\n                <View />\n            }\n        </View>\n    );\n}\n\nconst styles = StyleSheet.create({\n    container: {\n        flex: 1,\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 0,\n    },\n    fillParent: {\n        width: '100%',\n        height: '100%',\n        position: 'absolute',\n    },\n    audioVideoView: {\n        flex: 1,\n        zIndex: 2,\n        right: 0,\n        top: 0,\n    },\n});\n"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SAASC,kBAAkB,EAAEC,KAAK,QAAQ,cAAc;AAExD,SAASC,UAAU,EAAEC,IAAI,QAAQ,cAAc;AAC/C,OAAOC,SAAS,IAAIC,uBAAuB,EAAEC,cAAc,QAAQ,0BAA0B;AAC7F,OAAOC,wBAAwB,MAAM,4BAA4B;AACjE,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,kBAAkB,MAAM,sBAAsB;AACrD,OAAOC,qBAAqB,MAAM,yBAAyB;AAC3D,OAAOC,gBAAgB,MAAM,oBAAoB;AAGjD,eAAe,SAASC,gCAAgC,CAACC,KAAK,EAAE;EAC5D,MAAM;IACFC,KAAK;IACLC,OAAO;IACPC,MAAM;IACNC,QAAQ;IACRC,YAAY;IACZC,MAAM;IACNC,KAAK;IACLC;EACJ,CAAC,GAAGR,KAAK;EACT,MAAM;IACFS,oBAAoB,GAAG,CAAC,CAAC;IAEzBC,uBAAuB,GAAG,IAAI;IAC9BC,2BAA2B,GAAG,IAAI;IAClCC,qBAAqB,GAAG,IAAI;IAE5BC,mBAAmB,GAAG,CAAC,CAAC;IACxBC,gBAAgB,GAAG,CAAC,CAAC;IACrBC,gBAAgB,GAAG,CAAC,CAAC;IAErBC,MAAM,GAAG;MACLC,IAAI,EAAEzB,cAAc,CAAC0B,OAAO;MAC5BZ,MAAM,EAAE;QACJa,oCAAoC,EAAE;MAC1C;IACJ,CAAC;IAEDC,sBAAsB;IAAE;;IAExBC,OAAO;IACPC;EACJ,CAAC,GAAGhB,MAAM;EACV,MAAM;IACFiB,yBAAyB,GAAG,IAAI;IAChCC,qBAAqB,GAAG,KAAK;IAC7BC,kBAAkB,GAAG,IAAI;IACzBC,yBAAyB,GAAG,IAAI;IAChCC,sBAAsB,GAAG,KAAK;IAC9BC;EACJ,CAAC,GAAGnB,oBAAoB;EACxB,MAAM;IACFoB,OAAO,GAAG,CACNhC,qBAAqB,CAACiC,kBAAkB,EACxCjC,qBAAqB,CAACkC,kBAAkB,EACxClC,qBAAqB,CAACmC,WAAW,EACjCnC,qBAAqB,CAACoC,sBAAsB,EAC5CpC,qBAAqB,CAACqC,uBAAuB,CAChD;IACDC,QAAQ,GAAG,CAAC;IACZC,aAAa,GAAG,EAAE;IAClBC,iBAAiB,GAAG,IAAI;IACxBC,WAAW,GAAG,IAAI;IAClBC,KAAK,GAAGzC,gBAAgB,CAAC0C;EAC7B,CAAC,GAAG3B,mBAAmB;EACvB,MAAM;IACF4B,SAAS,GAAG,IAAI;IAChBC,KAAK,EAACC,QAAQ,GAAG,SAAS;IAC1Bd,OAAO,EAACe,UAAU,GAAG,CAAE/C,qBAAqB,CAACgD,oBAAoB,CAAE;IACnEV,QAAQ,EAACW,WAAW,GAAG,CAAC;IACxBV,aAAa,EAACW,gBAAgB,GAAG,EAAE;IACnCV,iBAAiB,EAACW,oBAAoB,GAAG,IAAI;IAC7CV,WAAW,EAACW,cAAc,GAAG,IAAI;IACjCV,KAAK,EAACW,QAAQ,GAAGpD,gBAAgB,CAAC0C;EACtC,CAAC,GAAG1B,gBAAgB;EACpB,MAAM;IACFqC,mBAAmB,GAAG,IAAI;IAC1BC,eAAe,GAAG,IAAI;IACtBC;EACJ,CAAC,GAAGtC,gBAAgB;EAEpB,MAAM,CAACuC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGvE,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAACwE,mBAAmB,EAAEC,sBAAsB,CAAC,GAAGzE,QAAQ,CAAC,IAAI,CAAC;EACpE,MAAM,CAAC0E,uBAAuB,EAAEC,0BAA0B,CAAC,GAAG3E,QAAQ,CAAC,KAAK,CAAC;EAC7E,IAAI4E,aAAa,GAAG,CAAC;EACrB,IAAIC,sBAAsB,GAAG,CAAC;EAE9B,MAAMC,eAAe,GAAG,MAAM;IAC1BF,aAAa,GAAG,CAAC;IACjBC,sBAAsB,GAAG,CAAC;IAC1B,IAAIP,gBAAgB,EAAE;MAClB,IAAIhB,WAAW,EAAE;QACbiB,mBAAmB,CAAC,KAAK,CAAC;QAC1BI,0BAA0B,CAAC,KAAK,CAAC;MACrC;IACJ,CAAC,MAAM;MACHJ,mBAAmB,CAAC,IAAI,CAAC;IAC7B;IACA,IAAIC,mBAAmB,EAAE;MACrB,IAAIP,cAAc,EAAE;QAChBQ,sBAAsB,CAAC,KAAK,CAAC;QAC7BE,0BAA0B,CAAC,KAAK,CAAC;MACrC;IACJ,CAAC,MAAM;MACHF,sBAAsB,CAAC,IAAI,CAAC;IAChC;EACJ,CAAC;EACD,MAAMM,gBAAgB,GAAG,MAAOC,QAAQ,IAAK;IACzC;IACA,IAAIC,QAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;MAC3B;MACA,IAAIC,YAAY,GAAGjF,kBAAkB,CAACkF,KAAK,CACvClF,kBAAkB,CAACmF,WAAW,CAACC,YAAY,CAC9C;MACD,IAAIC,aAAa,GAAGrF,kBAAkB,CAACkF,KAAK,CACxClF,kBAAkB,CAACmF,WAAW,CAACG,MAAM,CACxC;MACD,MAAMC,oBAAoB,GAAG,EAAE;MAC/B,IAAI;QACA,MAAMC,cAAc,GAAG,MAAMP,YAAY;QACzC,MAAMQ,cAAc,GAAG,MAAMJ,aAAa;QAC1C,IAAI,CAACG,cAAc,EAAE;UACjBD,oBAAoB,CAACG,IAAI,CACrB1F,kBAAkB,CAACmF,WAAW,CAACC,YAAY,CAC9C;QACL;QACA,IAAI,CAACK,cAAc,EAAE;UACjBF,oBAAoB,CAACG,IAAI,CAAC1F,kBAAkB,CAACmF,WAAW,CAACG,MAAM,CAAC;QACpE;MACJ,CAAC,CAAC,OAAOK,KAAK,EAAE;QACZJ,oBAAoB,CAACG,IAAI,CACrB1F,kBAAkB,CAACmF,WAAW,CAACC,YAAY,EAC3CpF,kBAAkB,CAACmF,WAAW,CAACG,MAAM,CACxC;MACL;MACA;MACA,OAAOtF,kBAAkB,CAAC4F,eAAe,CAACL,oBAAoB,CAAC,CAACM,IAAI,CAChEC,IAAI,IAAI;QACJC,OAAO,CAACC,IAAI,CAAC,iBAAiB,EAAEF,IAAI,CAAC;QACrC,IAAIhB,QAAQ,EAAE;UACVA,QAAQ,EAAE;QACd;MACJ,CAAC,CACJ;IACL,CAAC,MAAM,IAAIA,QAAQ,EAAE;MACjBA,QAAQ,EAAE;IACd;EACJ,CAAC;EACD;EACA,MAAMmB,cAAc,GAAG,MAAM;IACzB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACpC,IAAIlE,sBAAsB,EAAE;QACxB,MAAM;UACFsB,KAAK,GAAG,gBAAgB;UACxB6C,OAAO,GAAG,iCAAiC;UAC3CC,gBAAgB,GAAG,QAAQ;UAC3BC,iBAAiB,GAAG;QACxB,CAAC,GAAGrE,sBAAsB;QAC1BjC,KAAK,CAACuG,KAAK,CACPhD,KAAK,EACL6C,OAAO,EACP,CACI;UACII,IAAI,EAAEH,gBAAgB;UACtBI,OAAO,EAAE,MAAM;YACXN,MAAM,EAAE;UACZ,CAAC;UACD/C,KAAK,EAAE;QACX,CAAC,EACD;UACIoD,IAAI,EAAEF,iBAAiB;UACvBG,OAAO,EAAE,MAAM;YACXP,OAAO,EAAE;UACb;QACJ,CAAC,CACJ,EACD;UACIQ,UAAU,EAAE;QAChB,CAAC,CACJ;MACL,CAAC,MAAM;QACHR,OAAO,EAAE;MACb;IACJ,CAAC,CAAC;EACN,CAAC;EAEDtG,SAAS,CAAC,MAAM;IACZ,MAAM+G,UAAU,GAAG,kCAAkC,GAAGC,MAAM,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC;IACjG5G,SAAS,CAACkB,iBAAiB,CAACsF,UAAU,EAAEtF,iBAAiB,CAAC;IAC1D,OAAO,MAAM;MACTlB,SAAS,CAACkB,iBAAiB,CAACsF,UAAU,CAAC;IAC3C,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EACN/G,SAAS,CAAC,MAAM;IACZO,SAAS,CAAC6G,IAAI,CACVlG,KAAK,EACLC,OAAO,EACP;MAAEC,MAAM,EAAEA,MAAM;MAAEC,QAAQ,EAAEA;IAAS,CAAC,CAAC,CAAC2E,IAAI,CAAC,MAAM;MAE/CzF,SAAS,CAAC8G,YAAY,CAAC,EAAE,EAAE1F,uBAAuB,CAAC;MACnDpB,SAAS,CAAC+G,gBAAgB,CAAC,EAAE,EAAE1F,2BAA2B,CAAC;MAC3DrB,SAAS,CAACgH,uBAAuB,CAAC1F,qBAAqB,CAAC;MAExDmD,gBAAgB,CAAC,MAAM;QACnB,IAAI7D,OAAO,EAAE;UACTZ,SAAS,CAACiH,QAAQ,CAAClG,YAAY,CAAC;QACpC,CAAC,MAAM;UACHf,SAAS,CAACiH,QAAQ,CAAClG,YAAY,EAAEE,KAAK,IAAIC,iBAAiB,EAAE,CAAC;QAClE;MACJ,CAAC,CAAC;IAEN,CAAC,CAAC;IAEN,OAAO,MAAM;MACTlB,SAAS,CAACkH,SAAS,EAAE;IACzB,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,SAASC,WAAW,CAACzC,QAAQ,EAAE0C,KAAK,EAAE;IAClC,MAAMC,aAAa,GAAG1H,MAAM,EAAE;IAE9BF,SAAS,CAAC,MAAM;MACZ4H,aAAa,CAACC,OAAO,GAAG5C,QAAQ;IACpC,CAAC,CAAC;IAEFjF,SAAS,CAAC,MAAM;MACZ,SAAS8H,IAAI,GAAG;QACZF,aAAa,CAACC,OAAO,EAAE;MAC3B;MACA,IAAIF,KAAK,KAAK,IAAI,EAAE;QAChB,IAAII,EAAE,GAAGC,WAAW,CAACF,IAAI,EAAEH,KAAK,CAAC;QACjC,OAAO,MAAMM,aAAa,CAACF,EAAE,CAAC;MAClC;IACJ,CAAC,EAAE,CAACJ,KAAK,CAAC,CAAC;EACf;EAEA,SAASO,oBAAoB,GAAG;IAC5BtD,0BAA0B,CAAC,IAAI,CAAC;EACpC;EAEA,SAASuD,qBAAqB,GAAG;IAC7BvD,0BAA0B,CAAC,KAAK,CAAC;EACrC;EAEA8C,WAAW,CAAC,MAAM;IACd7C,aAAa,EAAE;IACf,IAAIA,aAAa,IAAI,CAAC,EAAE;MACpBA,aAAa,GAAG,CAAC;MACjB,IAAIvB,iBAAiB,EAAE;QACnBkB,mBAAmB,CAAC,KAAK,CAAC;MAC9B;IACJ;EACJ,CAAC,EAAE,IAAI,CAAC;EAERkD,WAAW,CAAC,MAAM;IACd5C,sBAAsB,EAAE;IACxB,IAAIA,sBAAsB,IAAI,CAAC,EAAE;MAC7BA,sBAAsB,GAAG,CAAC;MAC1B,IAAIb,oBAAoB,EAAE;QACtBS,sBAAsB,CAAC,KAAK,CAAC;MACjC;IACJ;EACJ,CAAC,EAAE,IAAI,CAAC;EAER,oBACI,oBAAC,IAAI;IAAC,KAAK,EAAE,CAAC0D,MAAM,CAACC,SAAS,EAAED,MAAM,CAACE,UAAU;EAAE,GAC9C5E,SAAS,IAAIe,mBAAmB,gBAC7B,oBAAC,cAAc;IACX,SAAS,EAAEb,QAAS;IACpB,sBAAsB,EAAEG,WAAY;IACpC,cAAc,EAAEF,UAAW;IAC3B,sBAAsB,EAAEG,gBAAiB;IACzC,OAAO,EAAE1B,OAAQ;IACjB,mBAAmB,EAAEC,mBAAmB,GAAGA,mBAAmB,GAAG6D,cAAe;IAChF,uBAAuB,EAAEzE,uBAAwB;IACjD,2BAA2B,EAAEC,2BAA4B;IACzD,qBAAqB,EAAEC,qBAAsB;IAC7C,oBAAoB,EAAEqG;EAAqB,EAC7C,gBAAG,oBAAC,IAAI,OAAG,eAEjB,oBAAC,IAAI;IAAC,KAAK,EAAEE,MAAM,CAACE,UAAW;IAAC,aAAa,EAAC,MAAM;IAAC,YAAY,EAAEvD;EAAgB,gBAC/E,oBAAC,uBAAuB;IAAC,KAAK,EAAE,CAACqD,MAAM,CAACG,cAAc,EAAEH,MAAM,CAACE,UAAU,CAAE;IACvE,gBAAgB,EAAE;MACd3F,yBAAyB,EAAEA,yBAAyB;MACpDC,sBAAsB,EAAEA;IAC5B,CAAE;IACF,MAAM,EAAEX,MAAO;IACf,iBAAiB,EAAEY,iBAAiB,GAAGA,iBAAiB,GAAG;MAAA,IAAC;QAAE2F;MAAS,CAAC;MAAA,oBACpE,oBAAC,wBAAwB;QACrB,QAAQ,EAAEA,QAAS;QACnB,yBAAyB,EAAEhG,yBAA0B;QACrD,qBAAqB,EAAEC,qBAAsB;QAC7C,kBAAkB,EAAEC;MAAmB,EACzC;IAAA;EACL,EACH,CACC,EACN6B,gBAAgB,gBACb,oBAAC,aAAa;IACV,sBAAsB,EAAEnB,QAAS;IACjC,cAAc,EAAEN,OAAQ;IACxB,sBAAsB,EAAEO,aAAc;IACtC,OAAO,EAAEf,OAAQ;IACjB,mBAAmB,EAAEC,mBAAmB,GAAGA,mBAAmB,GAAG6D,cAAe;IAChF,uBAAuB,EAAEzE,uBAAwB;IACjD,2BAA2B,EAAEC,2BAA4B;IACzD,qBAAqB,EAAEC,qBAAsB;IAC7C,WAAW,EAAE,MAAM;MAAEgD,aAAa,GAAG,CAAC;IAAE;EAAE,EAC5C,gBACF,oBAAC,IAAI,OAAG,EAEXF,uBAAuB,gBACpB,oBAAC,kBAAkB;IACf,mBAAmB,EAAEP,mBAAoB;IACzC,eAAe,EAAEC,eAAgB;IACjC,WAAW,EAAEC,WAAY;IACzB,qBAAqB,EAAE6D;EAAsB,EAC/C,gBACF,oBAAC,IAAI,OAAG,CAET;AAEf;AAEA,MAAMC,MAAM,GAAG/H,UAAU,CAACoI,MAAM,CAAC;EAC7BJ,SAAS,EAAE;IACPK,IAAI,EAAE,CAAC;IACPC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,MAAM,EAAE;EACZ,CAAC;EACDP,UAAU,EAAE;IACRQ,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE;EACd,CAAC;EACDT,cAAc,EAAE;IACZG,IAAI,EAAE,CAAC;IACPG,MAAM,EAAE,CAAC;IACTI,KAAK,EAAE,CAAC;IACRC,GAAG,EAAE;EACT;AACJ,CAAC,CAAC"}