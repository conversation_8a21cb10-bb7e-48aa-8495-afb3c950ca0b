{"version": 3, "names": ["React", "useState", "useEffect", "View", "StyleSheet", "TouchableOpacity", "ZegoLeaveButton", "ZegoSwitchAudioOutputButton", "ZegoSwitchCameraButton", "ZegoToggleCameraButton", "ZegoToggleMicrophoneButton", "ZegoMoreButton", "ZegoMenuBarButtonName", "ZegoBottomBar", "props", "menuBarButtonsMaxCount", "menuBarButtons", "menuBarExtendedButtons", "onLeave", "onLeaveConfirmation", "turnOnCameraWhenJoining", "turnOnMicrophoneWhenJoining", "useSpeakerWhenJoining", "onMorePress", "isNormalStyle", "setIsNormalStyle", "getButtonByButtonIndex", "buttonIndex", "toggleCameraButton", "toggleMicrophoneButton", "leaveButton", "switchAudioOutputButton", "switchCameraButton", "getDisplayButtons", "maxCount", "need<PERSON>ore<PERSON><PERSON>on", "length", "firstLevelButtons", "secondLevelButtons", "for<PERSON>ach", "limitCount", "push", "button", "getButtonStyle", "btnStyles", "styles", "ctrlBtn1", "ctrlBtn2", "ctrlBtn3", "ctrlBtn4", "ctrlBtn5", "allButtons", "normalBar", "map", "index", "popup<PERSON><PERSON><PERSON>", "fillParent", "popupMask", "popupBar", "marginBottom", "marginRight", "marginLeft", "create", "flex", "position", "flexDirection", "justifyContent", "alignItems", "width", "bottom", "height", "zIndex", "backgroundColor", "opacity", "paddingTop", "paddingBottom", "paddingLeft", "paddingRight", "flexWrap"], "sources": ["ZegoBottomBar.js"], "sourcesContent": ["import React, { useState, useEffect } from \"react\";\nimport { View, StyleSheet, TouchableOpacity } from \"react-native\";\nimport {\n    ZegoLeaveButton,\n    ZegoSwitchAudioOutputButton,\n    ZegoSwitchCameraButton,\n    ZegoToggleCameraButton,\n    ZegoToggleMicrophoneButton\n} from '@zegocloud/zego-uikit-rn'\n\nimport ZegoMoreButton from './ZegoMoreButton';\nimport ZegoMenuBarButtonName from \"./ZegoMenuBarButtonName\";\n\n\nexport default function ZegoBottomBar(props) {\n    const {\n        menuBarButtonsMaxCount = 5,\n        menuBarButtons = [],\n        menuBarExtendedButtons = [],\n        onLeave,\n        onLeaveConfirmation,\n        turnOnCameraWhenJoining,\n        turnOnMicrophoneWhenJoining,\n        useSpeakerWhenJoining,\n        onMorePress,\n    } = props;\n    const [isNormalStyle, setIsNormalStyle] = useState(true);\n\n    const getButtonByButtonIndex = (buttonIndex) => {\n        switch (buttonIndex) {\n            case ZegoMenuBarButtonName.toggleCameraButton:\n                return <ZegoToggleCameraButton key={1} isOn={turnOnCameraWhenJoining} />;\n            case ZegoMenuBarButtonName.toggleMicrophoneButton:\n                return <ZegoToggleMicrophoneButton key={2} isOn={turnOnMicrophoneWhenJoining} />;\n            case ZegoMenuBarButtonName.leaveButton:\n                return <ZegoLeaveButton key={0} onLeaveConfirmation={onLeaveConfirmation} onPressed={onLeave} />\n            case ZegoMenuBarButtonName.switchAudioOutputButton:\n                return <ZegoSwitchAudioOutputButton key={4} useSpeaker={useSpeakerWhenJoining} />\n            case ZegoMenuBarButtonName.switchCameraButton:\n                return <ZegoSwitchCameraButton key={3} />\n        }\n    }\n    const getDisplayButtons = () => {\n        var maxCount = menuBarButtonsMaxCount < 1 ? 1 : menuBarButtonsMaxCount;\n        maxCount = maxCount > 5 ? 5 : maxCount;\n        const needMoreButton = (menuBarButtons.length + menuBarExtendedButtons.length) > maxCount;\n        const firstLevelButtons = [];\n        const secondLevelButtons = [];\n        menuBarButtons.forEach(buttonIndex => {\n            const limitCount = needMoreButton ? maxCount - 1 : maxCount;\n            if (firstLevelButtons.length < limitCount) {\n                firstLevelButtons.push(getButtonByButtonIndex(buttonIndex));\n            } else {\n                secondLevelButtons.push(getButtonByButtonIndex(buttonIndex));\n            }\n        });\n        menuBarExtendedButtons.forEach(button => {\n            const limitCount = needMoreButton ? maxCount - 1 : maxCount;\n            if (firstLevelButtons.length < limitCount) {\n                firstLevelButtons.push(button);\n            } else {\n                secondLevelButtons.push(button);\n            }\n        });\n        if (needMoreButton) {\n            firstLevelButtons.push(<ZegoMoreButton onPress={() => { setIsNormalStyle(false); if (onMorePress) onMorePress() }} />)\n        }\n        return {\n            firstLevelButtons: firstLevelButtons,\n            secondLevelButtons: secondLevelButtons\n        }\n    }\n    const getButtonStyle = () => {\n        const btnStyles = [styles.ctrlBtn1, styles.ctrlBtn2, styles.ctrlBtn3, styles.ctrlBtn4, styles.ctrlBtn5,]\n        return btnStyles[firstLevelButtons.length - 1]\n    }\n\n    var allButtons = getDisplayButtons();\n    var firstLevelButtons = allButtons['firstLevelButtons']\n    var secondLevelButtons = allButtons['secondLevelButtons']\n\n    return (\n        isNormalStyle ?\n            <View style={styles.normalBar}>\n                {firstLevelButtons.map((button, index) => (\n                    <View key={index} style={getButtonStyle()}>\n                        {button}\n                    </View>\n                ))}\n            </View> :\n            <View style={[styles.popupContainer, styles.fillParent]}>\n                <View style={[styles.popupMask, styles.fillParent]} >\n                    <TouchableOpacity style={styles.fillParent} onPress={() => { setIsNormalStyle(true) }} />\n                </View>\n                <View style={styles.popupBar}>\n                    {secondLevelButtons.map((button, index) => (\n                        <View key={index} style={{ marginBottom: 20, marginRight: 32 / 2, marginLeft: 32 / 2 }}>\n                            {button}\n                        </View>\n                    ))}\n                </View>\n            </View>\n    );\n}\n\nconst styles = StyleSheet.create({\n    normalBar: {\n        flex: 1,\n        position: 'absolute',\n        flexDirection: 'row',\n        justifyContent: 'center',\n        alignItems: 'flex-end',\n        marginBottom: 50,\n        width: '100%',\n        bottom: 0,\n        height: 50,\n        zIndex: 2,\n    },\n    popupContainer: {\n        flex: 1,\n        justifyContent: 'flex-end',\n    },\n    fillParent: {\n        position: 'absolute',\n        width: '100%',\n        height: '100%',\n    },\n    popupMask: {\n        backgroundColor: '#262A2D',\n        opacity: 0.3,\n    },\n    popupBar: {\n        flex: 1,\n        paddingTop: 27,\n        paddingBottom: 3,\n        paddingLeft: 28.5,\n        paddingRight: 28.5,\n        position: 'absolute',\n        flexDirection: 'row',\n        flexWrap: \"wrap\",\n        justifyContent: 'flex-start',\n        alignItems: 'flex-end',\n        width: '100%',\n        bottom: 0,\n        zIndex: 2,\n        backgroundColor: '#262A2D'\n    },\n    ctrlBtn1: {\n        marginLeft: 0,\n        marginRight: 0,\n    },\n    ctrlBtn2: {\n        marginLeft: 79 / 2,\n        marginRight: 79 / 2,\n    },\n    ctrlBtn3: {\n        marginLeft: 59.5 / 2,\n        marginRight: 59.5 / 2,\n    },\n    ctrlBtn4: {\n        marginLeft: 37 / 2,\n        marginRight: 37 / 2,\n    },\n    ctrlBtn5: {\n        marginLeft: 23 / 2,\n        marginRight: 23 / 2,\n    }\n});"], "mappings": "AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,IAAI,EAAEC,UAAU,EAAEC,gBAAgB,QAAQ,cAAc;AACjE,SACIC,eAAe,EACfC,2BAA2B,EAC3BC,sBAAsB,EACtBC,sBAAsB,EACtBC,0BAA0B,QACvB,0BAA0B;AAEjC,OAAOC,cAAc,MAAM,kBAAkB;AAC7C,OAAOC,qBAAqB,MAAM,yBAAyB;AAG3D,eAAe,SAASC,aAAa,CAACC,KAAK,EAAE;EACzC,MAAM;IACFC,sBAAsB,GAAG,CAAC;IAC1BC,cAAc,GAAG,EAAE;IACnBC,sBAAsB,GAAG,EAAE;IAC3BC,OAAO;IACPC,mBAAmB;IACnBC,uBAAuB;IACvBC,2BAA2B;IAC3BC,qBAAqB;IACrBC;EACJ,CAAC,GAAGT,KAAK;EACT,MAAM,CAACU,aAAa,EAAEC,gBAAgB,CAAC,GAAGxB,QAAQ,CAAC,IAAI,CAAC;EAExD,MAAMyB,sBAAsB,GAAIC,WAAW,IAAK;IAC5C,QAAQA,WAAW;MACf,KAAKf,qBAAqB,CAACgB,kBAAkB;QACzC,oBAAO,oBAAC,sBAAsB;UAAC,GAAG,EAAE,CAAE;UAAC,IAAI,EAAER;QAAwB,EAAG;MAC5E,KAAKR,qBAAqB,CAACiB,sBAAsB;QAC7C,oBAAO,oBAAC,0BAA0B;UAAC,GAAG,EAAE,CAAE;UAAC,IAAI,EAAER;QAA4B,EAAG;MACpF,KAAKT,qBAAqB,CAACkB,WAAW;QAClC,oBAAO,oBAAC,eAAe;UAAC,GAAG,EAAE,CAAE;UAAC,mBAAmB,EAAEX,mBAAoB;UAAC,SAAS,EAAED;QAAQ,EAAG;MACpG,KAAKN,qBAAqB,CAACmB,uBAAuB;QAC9C,oBAAO,oBAAC,2BAA2B;UAAC,GAAG,EAAE,CAAE;UAAC,UAAU,EAAET;QAAsB,EAAG;MACrF,KAAKV,qBAAqB,CAACoB,kBAAkB;QACzC,oBAAO,oBAAC,sBAAsB;UAAC,GAAG,EAAE;QAAE,EAAG;IAAA;EAErD,CAAC;EACD,MAAMC,iBAAiB,GAAG,MAAM;IAC5B,IAAIC,QAAQ,GAAGnB,sBAAsB,GAAG,CAAC,GAAG,CAAC,GAAGA,sBAAsB;IACtEmB,QAAQ,GAAGA,QAAQ,GAAG,CAAC,GAAG,CAAC,GAAGA,QAAQ;IACtC,MAAMC,cAAc,GAAInB,cAAc,CAACoB,MAAM,GAAGnB,sBAAsB,CAACmB,MAAM,GAAIF,QAAQ;IACzF,MAAMG,iBAAiB,GAAG,EAAE;IAC5B,MAAMC,kBAAkB,GAAG,EAAE;IAC7BtB,cAAc,CAACuB,OAAO,CAACZ,WAAW,IAAI;MAClC,MAAMa,UAAU,GAAGL,cAAc,GAAGD,QAAQ,GAAG,CAAC,GAAGA,QAAQ;MAC3D,IAAIG,iBAAiB,CAACD,MAAM,GAAGI,UAAU,EAAE;QACvCH,iBAAiB,CAACI,IAAI,CAACf,sBAAsB,CAACC,WAAW,CAAC,CAAC;MAC/D,CAAC,MAAM;QACHW,kBAAkB,CAACG,IAAI,CAACf,sBAAsB,CAACC,WAAW,CAAC,CAAC;MAChE;IACJ,CAAC,CAAC;IACFV,sBAAsB,CAACsB,OAAO,CAACG,MAAM,IAAI;MACrC,MAAMF,UAAU,GAAGL,cAAc,GAAGD,QAAQ,GAAG,CAAC,GAAGA,QAAQ;MAC3D,IAAIG,iBAAiB,CAACD,MAAM,GAAGI,UAAU,EAAE;QACvCH,iBAAiB,CAACI,IAAI,CAACC,MAAM,CAAC;MAClC,CAAC,MAAM;QACHJ,kBAAkB,CAACG,IAAI,CAACC,MAAM,CAAC;MACnC;IACJ,CAAC,CAAC;IACF,IAAIP,cAAc,EAAE;MAChBE,iBAAiB,CAACI,IAAI,eAAC,oBAAC,cAAc;QAAC,OAAO,EAAE,MAAM;UAAEhB,gBAAgB,CAAC,KAAK,CAAC;UAAE,IAAIF,WAAW,EAAEA,WAAW,EAAE;QAAC;MAAE,EAAG,CAAC;IAC1H;IACA,OAAO;MACHc,iBAAiB,EAAEA,iBAAiB;MACpCC,kBAAkB,EAAEA;IACxB,CAAC;EACL,CAAC;EACD,MAAMK,cAAc,GAAG,MAAM;IACzB,MAAMC,SAAS,GAAG,CAACC,MAAM,CAACC,QAAQ,EAAED,MAAM,CAACE,QAAQ,EAAEF,MAAM,CAACG,QAAQ,EAAEH,MAAM,CAACI,QAAQ,EAAEJ,MAAM,CAACK,QAAQ,CAAE;IACxG,OAAON,SAAS,CAACP,iBAAiB,CAACD,MAAM,GAAG,CAAC,CAAC;EAClD,CAAC;EAED,IAAIe,UAAU,GAAGlB,iBAAiB,EAAE;EACpC,IAAII,iBAAiB,GAAGc,UAAU,CAAC,mBAAmB,CAAC;EACvD,IAAIb,kBAAkB,GAAGa,UAAU,CAAC,oBAAoB,CAAC;EAEzD,OACI3B,aAAa,gBACT,oBAAC,IAAI;IAAC,KAAK,EAAEqB,MAAM,CAACO;EAAU,GACzBf,iBAAiB,CAACgB,GAAG,CAAC,CAACX,MAAM,EAAEY,KAAK,kBACjC,oBAAC,IAAI;IAAC,GAAG,EAAEA,KAAM;IAAC,KAAK,EAAEX,cAAc;EAAG,GACrCD,MAAM,CAEd,CAAC,CACC,gBACP,oBAAC,IAAI;IAAC,KAAK,EAAE,CAACG,MAAM,CAACU,cAAc,EAAEV,MAAM,CAACW,UAAU;EAAE,gBACpD,oBAAC,IAAI;IAAC,KAAK,EAAE,CAACX,MAAM,CAACY,SAAS,EAAEZ,MAAM,CAACW,UAAU;EAAE,gBAC/C,oBAAC,gBAAgB;IAAC,KAAK,EAAEX,MAAM,CAACW,UAAW;IAAC,OAAO,EAAE,MAAM;MAAE/B,gBAAgB,CAAC,IAAI,CAAC;IAAC;EAAE,EAAG,CACtF,eACP,oBAAC,IAAI;IAAC,KAAK,EAAEoB,MAAM,CAACa;EAAS,GACxBpB,kBAAkB,CAACe,GAAG,CAAC,CAACX,MAAM,EAAEY,KAAK,kBAClC,oBAAC,IAAI;IAAC,GAAG,EAAEA,KAAM;IAAC,KAAK,EAAE;MAAEK,YAAY,EAAE,EAAE;MAAEC,WAAW,EAAE,EAAE,GAAG,CAAC;MAAEC,UAAU,EAAE,EAAE,GAAG;IAAE;EAAE,GAClFnB,MAAM,CAEd,CAAC,CACC,CACJ;AAEnB;AAEA,MAAMG,MAAM,GAAGzC,UAAU,CAAC0D,MAAM,CAAC;EAC7BV,SAAS,EAAE;IACPW,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE,UAAU;IACpBC,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,UAAU;IACtBR,YAAY,EAAE,EAAE;IAChBS,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,CAAC;IACTC,MAAM,EAAE,EAAE;IACVC,MAAM,EAAE;EACZ,CAAC;EACDhB,cAAc,EAAE;IACZQ,IAAI,EAAE,CAAC;IACPG,cAAc,EAAE;EACpB,CAAC;EACDV,UAAU,EAAE;IACRQ,QAAQ,EAAE,UAAU;IACpBI,KAAK,EAAE,MAAM;IACbE,MAAM,EAAE;EACZ,CAAC;EACDb,SAAS,EAAE;IACPe,eAAe,EAAE,SAAS;IAC1BC,OAAO,EAAE;EACb,CAAC;EACDf,QAAQ,EAAE;IACNK,IAAI,EAAE,CAAC;IACPW,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE,CAAC;IAChBC,WAAW,EAAE,IAAI;IACjBC,YAAY,EAAE,IAAI;IAClBb,QAAQ,EAAE,UAAU;IACpBC,aAAa,EAAE,KAAK;IACpBa,QAAQ,EAAE,MAAM;IAChBZ,cAAc,EAAE,YAAY;IAC5BC,UAAU,EAAE,UAAU;IACtBC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,CAAC;IACTE,MAAM,EAAE,CAAC;IACTC,eAAe,EAAE;EACrB,CAAC;EACD1B,QAAQ,EAAE;IACNe,UAAU,EAAE,CAAC;IACbD,WAAW,EAAE;EACjB,CAAC;EACDb,QAAQ,EAAE;IACNc,UAAU,EAAE,EAAE,GAAG,CAAC;IAClBD,WAAW,EAAE,EAAE,GAAG;EACtB,CAAC;EACDZ,QAAQ,EAAE;IACNa,UAAU,EAAE,IAAI,GAAG,CAAC;IACpBD,WAAW,EAAE,IAAI,GAAG;EACxB,CAAC;EACDX,QAAQ,EAAE;IACNY,UAAU,EAAE,EAAE,GAAG,CAAC;IAClBD,WAAW,EAAE,EAAE,GAAG;EACtB,CAAC;EACDV,QAAQ,EAAE;IACNW,UAAU,EAAE,EAAE,GAAG,CAAC;IAClBD,WAAW,EAAE,EAAE,GAAG;EACtB;AACJ,CAAC,CAAC"}