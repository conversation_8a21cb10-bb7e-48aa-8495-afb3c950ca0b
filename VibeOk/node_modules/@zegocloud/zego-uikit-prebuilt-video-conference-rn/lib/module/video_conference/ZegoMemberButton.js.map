{"version": 3, "names": ["React", "View", "Image", "TouchableOpacity", "StyleSheet", "ZegoMemberButton", "props", "onPressed", "require"], "sources": ["ZegoMemberButton.js"], "sourcesContent": ["import React from \"react\";\nimport { View, Image, TouchableOpacity, StyleSheet } from \"react-native\";\n\nexport default function ZegoMemberButton(props) {\n    const { onPressed } = props;\n\n    return (<View>\n        <TouchableOpacity \n            onPress={onPressed}>\n            <Image\n                source={require('./resources/white_button_members.png')}\n            />\n        </TouchableOpacity>\n    </View>);\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,KAAK,EAAEC,gBAAgB,EAAEC,UAAU,QAAQ,cAAc;AAExE,eAAe,SAASC,gBAAgB,CAACC,KAAK,EAAE;EAC5C,MAAM;IAAEC;EAAU,CAAC,GAAGD,KAAK;EAE3B,oBAAQ,oBAAC,IAAI,qBACT,oBAAC,gBAAgB;IACb,OAAO,EAAEC;EAAU,gBACnB,oBAAC,KAAK;IACF,MAAM,EAAEC,OAAO,CAAC,sCAAsC;EAAE,EAC1D,CACa,CAChB;AACX"}