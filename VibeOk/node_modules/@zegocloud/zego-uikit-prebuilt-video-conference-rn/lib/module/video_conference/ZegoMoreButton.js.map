{"version": 3, "names": ["React", "View", "TouchableOpacity", "Image", "ZegoMoreButton", "props", "onPress", "require"], "sources": ["ZegoMoreButton.js"], "sourcesContent": ["import React from \"react\";\nimport { View, TouchableOpacity, Image } from \"react-native\"\n\nexport default function ZegoMoreButton(props) {\n    const { onPress } = props;\n\n    return (<View>\n        <TouchableOpacity\n            onPress={onPress}>\n            <Image source={require('./resources/white_button_more.png')} />\n        </TouchableOpacity>\n    </View>)\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,IAAI,EAAEC,gBAAgB,EAAEC,KAAK,QAAQ,cAAc;AAE5D,eAAe,SAASC,cAAc,CAACC,KAAK,EAAE;EAC1C,MAAM;IAAEC;EAAQ,CAAC,GAAGD,KAAK;EAEzB,oBAAQ,oBAAC,IAAI,qBACT,oBAAC,gBAAgB;IACb,OAAO,EAAEC;EAAQ,gBACjB,oBAAC,KAAK;IAAC,MAAM,EAAEC,OAAO,CAAC,mCAAmC;EAAE,EAAG,CAChD,CAChB;AACX"}