{"version": 3, "names": ["zloginfo", "msg", "console", "log", "zlogwarning", "warn", "zlogerror", "error"], "sources": ["logger.js"], "sourcesContent": ["export const zloginfo = (...msg) => {\n    console.log(\"ZEGOUIKit[INFO]: \", ...msg);\n}\nexport const zlogwarning = (msg) => {\n    console.warn(\"ZEGOUIKit[WARNING]: \", ...msg);\n}\n\nexport const zlogerror = (msg) => {\n    console.error(\"ZEGOUIKit[ERROR]: \", ...msg);\n}"], "mappings": ";;;;;;AAAO,MAAMA,QAAQ,GAAG,YAAY;EAAA,kCAARC,GAAG;IAAHA,GAAG;EAAA;EAC3BC,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAE,GAAGF,GAAG,CAAC;AAC5C,CAAC;AAAA;AACM,MAAMG,WAAW,GAAIH,GAAG,IAAK;EAChCC,OAAO,CAACG,IAAI,CAAC,sBAAsB,EAAE,GAAGJ,GAAG,CAAC;AAChD,CAAC;AAAA;AAEM,MAAMK,SAAS,GAAIL,GAAG,IAAK;EAC9BC,OAAO,CAACK,KAAK,CAAC,oBAAoB,EAAE,GAAGN,GAAG,CAAC;AAC/C,CAAC;AAAA"}