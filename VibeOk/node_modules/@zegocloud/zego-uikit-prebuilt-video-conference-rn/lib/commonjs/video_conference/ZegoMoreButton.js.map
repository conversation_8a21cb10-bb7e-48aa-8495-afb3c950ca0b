{"version": 3, "names": ["ZegoMoreButton", "props", "onPress", "require"], "sources": ["ZegoMoreButton.js"], "sourcesContent": ["import React from \"react\";\nimport { View, TouchableOpacity, Image } from \"react-native\"\n\nexport default function ZegoMoreButton(props) {\n    const { onPress } = props;\n\n    return (<View>\n        <TouchableOpacity\n            onPress={onPress}>\n            <Image source={require('./resources/white_button_more.png')} />\n        </TouchableOpacity>\n    </View>)\n}\n"], "mappings": ";;;;;;AAAA;AACA;AAA4D;AAE7C,SAASA,cAAc,CAACC,KAAK,EAAE;EAC1C,MAAM;IAAEC;EAAQ,CAAC,GAAGD,KAAK;EAEzB,oBAAQ,6BAAC,iBAAI,qBACT,6BAAC,6BAAgB;IACb,OAAO,EAAEC;EAAQ,gBACjB,6BAAC,kBAAK;IAAC,MAAM,EAAEC,OAAO,CAAC,mCAAmC;EAAE,EAAG,CAChD,CAChB;AACX"}