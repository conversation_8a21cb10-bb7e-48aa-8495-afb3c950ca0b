{"version": 3, "names": ["ZegoTopBar", "props", "menuTitle", "menuBarButtonsMaxCount", "menuBarButtons", "menuBarExtendedButtons", "onLeave", "onLeaveConfirmation", "onOpenCallMemberList", "turnOnCameraWhenJoining", "turnOnMicrophoneWhenJoining", "useSpeakerWhenJoining", "getButtonByButtonIndex", "buttonIndex", "ZegoMenuBarButtonName", "toggleCameraButton", "toggleMicrophoneButton", "leaveButton", "switchAudioOutputButton", "switchCameraButton", "showMemberListButton", "getDisplayButtons", "allButtons", "slice", "for<PERSON>ach", "push", "concat", "getButtonStyle", "styles", "customIconContainer", "topBarContainer", "left", "title", "right", "map", "button", "index", "StyleSheet", "create", "flex", "position", "flexDirection", "alignItems", "width", "top", "height", "zIndex", "justifyContent", "paddingLeft", "paddingRight", "opacity", "color", "fontSize", "marginLeft"], "sources": ["ZegoTopMenuBar.js"], "sourcesContent": ["import React, { useState } from \"react\";\nimport { View, Text, Image, TouchableWithoutFeedback, SafeAreaView, StyleSheet } from \"react-native\";\nimport ZegoMenuBarButtonName from \"./ZegoMenuBarButtonName\";\nimport ZegoMemberButton from \"./ZegoMemberButton\";\nimport {\n    ZegoLeaveButton,\n    ZegoSwitchAudioOutputButton,\n    ZegoSwitchCameraButton,\n    ZegoToggleCameraButton,\n    ZegoToggleMicrophoneButton\n} from '@zegocloud/zego-uikit-rn'\n\nexport default function ZegoTopBar(props) {\n    const {\n        menuTitle,\n        menuBarButtonsMaxCount,\n        menuBarButtons,\n        menuBarExtendedButtons,\n        onLeave,\n        onLeaveConfirmation,\n        onOpenCallMemberList,\n        turnOnCameraWhenJoining,\n        turnOnMicrophoneWhenJoining,\n        useSpeakerWhenJoining,\n    } = props;\n\n    const getButtonByButtonIndex = (buttonIndex) => {\n        switch (buttonIndex) {\n            case ZegoMenuBarButtonName.toggleCameraButton:\n                return <ZegoToggleCameraButton key={1} isOn={turnOnCameraWhenJoining} />;\n            case ZegoMenuBarButtonName.toggleMicrophoneButton:\n                return <ZegoToggleMicrophoneButton key={2} isOn={turnOnMicrophoneWhenJoining} />;\n            case ZegoMenuBarButtonName.leaveButton:\n                return <ZegoLeaveButton key={0} onLeaveConfirmation={onLeaveConfirmation} onPressed={onLeave} />\n            case ZegoMenuBarButtonName.switchAudioOutputButton:\n                return <ZegoSwitchAudioOutputButton key={4} useSpeaker={useSpeakerWhenJoining} />\n            case ZegoMenuBarButtonName.switchCameraButton:\n                return <ZegoSwitchCameraButton key={3} />\n            case ZegoMenuBarButtonName.showMemberListButton:\n                return <ZegoMemberButton key={5} onPressed={onOpenCallMemberList}/>\n        }\n    };\n    const getDisplayButtons = () => {\n        let allButtons = [];\n        menuBarButtons.slice(0, menuBarButtonsMaxCount).forEach(buttonIndex => {\n            allButtons.push(getButtonByButtonIndex(buttonIndex));\n        });\n        allButtons = allButtons.concat(menuBarExtendedButtons);\n        return allButtons;\n    };\n    const getButtonStyle = () => {\n        return styles.customIconContainer;\n    };\n\n    const allButtons = getDisplayButtons();\n\n    return (<View style={styles.topBarContainer}>\n        <View style={styles.left}>\n            {/* <TouchableWithoutFeedback>\n                <Image\n                    style={styles.downArrowIcon}\n                    source={require('./resources/white_button_arrow.png')}\n                />\n            </TouchableWithoutFeedback> */}\n            <Text style={styles.title}>{menuTitle}</Text>\n        </View>\n        <View style={styles.right}>{\n            allButtons.map((button, index) => <View key={index} style={getButtonStyle()}>{button}</View>)\n        }</View>\n    </View>);\n}\n\nconst styles = StyleSheet.create({\n    topBarContainer: {\n        flex: 1,\n        position: 'absolute',\n        flexDirection: 'row',\n        alignItems: 'center',\n        width: '100%',\n        top: 35,\n        height: 44,\n        zIndex: 3,\n        justifyContent: 'space-between',\n        paddingLeft: 3.5,\n        paddingRight: 13.5,\n    },\n    left: {\n        opacity: 1,\n        flexDirection: 'row',\n        alignItems: \"center\",\n    },\n    right: {\n        flexDirection: 'row',\n        alignItems: \"center\",\n    },\n    title: {\n        color: '#FFFFFF',\n        fontSize: 18,\n        marginLeft: 4,\n    },\n    customIconContainer: {\n        marginLeft: 10,\n    },\n});\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AAMiC;AAAA;AAAA;AAElB,SAASA,UAAU,CAACC,KAAK,EAAE;EACtC,MAAM;IACFC,SAAS;IACTC,sBAAsB;IACtBC,cAAc;IACdC,sBAAsB;IACtBC,OAAO;IACPC,mBAAmB;IACnBC,oBAAoB;IACpBC,uBAAuB;IACvBC,2BAA2B;IAC3BC;EACJ,CAAC,GAAGV,KAAK;EAET,MAAMW,sBAAsB,GAAIC,WAAW,IAAK;IAC5C,QAAQA,WAAW;MACf,KAAKC,8BAAqB,CAACC,kBAAkB;QACzC,oBAAO,6BAAC,mCAAsB;UAAC,GAAG,EAAE,CAAE;UAAC,IAAI,EAAEN;QAAwB,EAAG;MAC5E,KAAKK,8BAAqB,CAACE,sBAAsB;QAC7C,oBAAO,6BAAC,uCAA0B;UAAC,GAAG,EAAE,CAAE;UAAC,IAAI,EAAEN;QAA4B,EAAG;MACpF,KAAKI,8BAAqB,CAACG,WAAW;QAClC,oBAAO,6BAAC,4BAAe;UAAC,GAAG,EAAE,CAAE;UAAC,mBAAmB,EAAEV,mBAAoB;UAAC,SAAS,EAAED;QAAQ,EAAG;MACpG,KAAKQ,8BAAqB,CAACI,uBAAuB;QAC9C,oBAAO,6BAAC,wCAA2B;UAAC,GAAG,EAAE,CAAE;UAAC,UAAU,EAAEP;QAAsB,EAAG;MACrF,KAAKG,8BAAqB,CAACK,kBAAkB;QACzC,oBAAO,6BAAC,mCAAsB;UAAC,GAAG,EAAE;QAAE,EAAG;MAC7C,KAAKL,8BAAqB,CAACM,oBAAoB;QAC3C,oBAAO,6BAAC,yBAAgB;UAAC,GAAG,EAAE,CAAE;UAAC,SAAS,EAAEZ;QAAqB,EAAE;IAAA;EAE/E,CAAC;EACD,MAAMa,iBAAiB,GAAG,MAAM;IAC5B,IAAIC,UAAU,GAAG,EAAE;IACnBlB,cAAc,CAACmB,KAAK,CAAC,CAAC,EAAEpB,sBAAsB,CAAC,CAACqB,OAAO,CAACX,WAAW,IAAI;MACnES,UAAU,CAACG,IAAI,CAACb,sBAAsB,CAACC,WAAW,CAAC,CAAC;IACxD,CAAC,CAAC;IACFS,UAAU,GAAGA,UAAU,CAACI,MAAM,CAACrB,sBAAsB,CAAC;IACtD,OAAOiB,UAAU;EACrB,CAAC;EACD,MAAMK,cAAc,GAAG,MAAM;IACzB,OAAOC,MAAM,CAACC,mBAAmB;EACrC,CAAC;EAED,MAAMP,UAAU,GAAGD,iBAAiB,EAAE;EAEtC,oBAAQ,6BAAC,iBAAI;IAAC,KAAK,EAAEO,MAAM,CAACE;EAAgB,gBACxC,6BAAC,iBAAI;IAAC,KAAK,EAAEF,MAAM,CAACG;EAAK,gBAOrB,6BAAC,iBAAI;IAAC,KAAK,EAAEH,MAAM,CAACI;EAAM,GAAE9B,SAAS,CAAQ,CAC1C,eACP,6BAAC,iBAAI;IAAC,KAAK,EAAE0B,MAAM,CAACK;EAAM,GACtBX,UAAU,CAACY,GAAG,CAAC,CAACC,MAAM,EAAEC,KAAK,kBAAK,6BAAC,iBAAI;IAAC,GAAG,EAAEA,KAAM;IAAC,KAAK,EAAET,cAAc;EAAG,GAAEQ,MAAM,CAAQ,CAAC,CACzF,CACL;AACX;AAEA,MAAMP,MAAM,GAAGS,uBAAU,CAACC,MAAM,CAAC;EAC7BR,eAAe,EAAE;IACbS,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE,UAAU;IACpBC,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,KAAK,EAAE,MAAM;IACbC,GAAG,EAAE,EAAE;IACPC,MAAM,EAAE,EAAE;IACVC,MAAM,EAAE,CAAC;IACTC,cAAc,EAAE,eAAe;IAC/BC,WAAW,EAAE,GAAG;IAChBC,YAAY,EAAE;EAClB,CAAC;EACDlB,IAAI,EAAE;IACFmB,OAAO,EAAE,CAAC;IACVT,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE;EAChB,CAAC;EACDT,KAAK,EAAE;IACHQ,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE;EAChB,CAAC;EACDV,KAAK,EAAE;IACHmB,KAAK,EAAE,SAAS;IAChBC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE;EAChB,CAAC;EACDxB,mBAAmB,EAAE;IACjBwB,UAAU,EAAE;EAChB;AACJ,CAAC,CAAC"}