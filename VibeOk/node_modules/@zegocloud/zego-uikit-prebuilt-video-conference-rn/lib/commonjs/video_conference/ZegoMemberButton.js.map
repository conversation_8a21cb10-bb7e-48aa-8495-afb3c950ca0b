{"version": 3, "names": ["ZegoMemberButton", "props", "onPressed", "require"], "sources": ["ZegoMemberButton.js"], "sourcesContent": ["import React from \"react\";\nimport { View, Image, TouchableOpacity, StyleSheet } from \"react-native\";\n\nexport default function ZegoMemberButton(props) {\n    const { onPressed } = props;\n\n    return (<View>\n        <TouchableOpacity \n            onPress={onPressed}>\n            <Image\n                source={require('./resources/white_button_members.png')}\n            />\n        </TouchableOpacity>\n    </View>);\n}\n"], "mappings": ";;;;;;AAAA;AACA;AAAyE;AAE1D,SAASA,gBAAgB,CAACC,KAAK,EAAE;EAC5C,MAAM;IAAEC;EAAU,CAAC,GAAGD,KAAK;EAE3B,oBAAQ,6BAAC,iBAAI,qBACT,6BAAC,6BAAgB;IACb,OAAO,EAAEC;EAAU,gBACnB,6BAAC,kBAAK;IACF,MAAM,EAAEC,OAAO,CAAC,sCAAsC;EAAE,EAC1D,CACa,CAChB;AACX"}