{"version": 3, "names": ["ZegoCallMemberList", "props", "showMicrophoneState", "showCameraState", "itemBuilder", "onCloseCallMemberList", "styles", "container", "header", "downArrowIcon", "require", "title", "divide", "memberListContainer", "StyleSheet", "create", "flex", "overflow", "borderTopLeftRadius", "borderTopRightRadius", "backgroundColor", "width", "height", "zIndex", "position", "bottom", "flexDirection", "alignItems", "marginLeft", "marginRight", "fontSize", "color", "opacity", "paddingTop", "paddingBottom"], "sources": ["ZegoCallMemberList.js"], "sourcesContent": ["import React from \"react\";\nimport { ZegoMemberList } from '@zegocloud/zego-uikit-rn';\nimport { StyleSheet, View, Image, Text, TouchableWithoutFeedback } from \"react-native\"\n\nexport default function ZegoCallMemberList(props) {\n    const {\n        showMicrophoneState,\n        showCameraState,\n        itemBuilder,\n        onCloseCallMemberList\n    } = props;\n    \n    return (<View style={styles.container}>\n        <View style={styles.header}>\n            <TouchableWithoutFeedback\n                onPress={onCloseCallMemberList}>\n                <Image\n                    style={styles.downArrowIcon}\n                    source={require('./resources/white_button_back.png')}\n                />\n            </TouchableWithoutFeedback>\n            <Text style={styles.title}>Member</Text>\n        </View>\n        <View style={styles.divide}></View>\n        <View style={styles.memberListContainer}>\n            <ZegoMemberList \n                showMicrophoneState={showMicrophoneState}\n                showCameraState={showCameraState}\n                itemBuilder={itemBuilder}\n            />\n        </View>\n    </View>);\n}\n\nconst styles = StyleSheet.create({\n    container: {\n        flex: 1,\n        overflow: 'hidden',\n        borderTopLeftRadius: 40,\n        borderTopRightRadius: 40,\n        backgroundColor: 'rgba(34,34,34,0.8)',\n        width: '100%',\n        height: 571,\n        zIndex: 4,\n        position: 'absolute',\n        bottom: 0,\n        flexDirection: 'column',\n    },\n    header: {\n        flexDirection: 'row',\n        alignItems: 'center',\n        height: 49,\n    },\n    downArrowIcon: {\n        marginLeft: 11.5,\n        marginRight: 5,\n    },\n    title: {\n        fontSize: 18,\n        color: '#FFFFFF',\n    },\n    divide: {\n        width: '100%',\n        height: 1,\n        backgroundColor: '#FFFFFF',\n        opacity: 0.15,\n    },\n    memberListContainer: {\n        flex: 1,\n        paddingTop: 12,\n        paddingBottom: 14,\n    },\n});\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AAAsF;AAEvE,SAASA,kBAAkB,CAACC,KAAK,EAAE;EAC9C,MAAM;IACFC,mBAAmB;IACnBC,eAAe;IACfC,WAAW;IACXC;EACJ,CAAC,GAAGJ,KAAK;EAET,oBAAQ,6BAAC,iBAAI;IAAC,KAAK,EAAEK,MAAM,CAACC;EAAU,gBAClC,6BAAC,iBAAI;IAAC,KAAK,EAAED,MAAM,CAACE;EAAO,gBACvB,6BAAC,qCAAwB;IACrB,OAAO,EAAEH;EAAsB,gBAC/B,6BAAC,kBAAK;IACF,KAAK,EAAEC,MAAM,CAACG,aAAc;IAC5B,MAAM,EAAEC,OAAO,CAAC,mCAAmC;EAAE,EACvD,CACqB,eAC3B,6BAAC,iBAAI;IAAC,KAAK,EAAEJ,MAAM,CAACK;EAAM,GAAC,QAAM,CAAO,CACrC,eACP,6BAAC,iBAAI;IAAC,KAAK,EAAEL,MAAM,CAACM;EAAO,EAAQ,eACnC,6BAAC,iBAAI;IAAC,KAAK,EAAEN,MAAM,CAACO;EAAoB,gBACpC,6BAAC,2BAAc;IACX,mBAAmB,EAAEX,mBAAoB;IACzC,eAAe,EAAEC,eAAgB;IACjC,WAAW,EAAEC;EAAY,EAC3B,CACC,CACJ;AACX;AAEA,MAAME,MAAM,GAAGQ,uBAAU,CAACC,MAAM,CAAC;EAC7BR,SAAS,EAAE;IACPS,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE,QAAQ;IAClBC,mBAAmB,EAAE,EAAE;IACvBC,oBAAoB,EAAE,EAAE;IACxBC,eAAe,EAAE,oBAAoB;IACrCC,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,GAAG;IACXC,MAAM,EAAE,CAAC;IACTC,QAAQ,EAAE,UAAU;IACpBC,MAAM,EAAE,CAAC;IACTC,aAAa,EAAE;EACnB,CAAC;EACDlB,MAAM,EAAE;IACJkB,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBL,MAAM,EAAE;EACZ,CAAC;EACDb,aAAa,EAAE;IACXmB,UAAU,EAAE,IAAI;IAChBC,WAAW,EAAE;EACjB,CAAC;EACDlB,KAAK,EAAE;IACHmB,QAAQ,EAAE,EAAE;IACZC,KAAK,EAAE;EACX,CAAC;EACDnB,MAAM,EAAE;IACJS,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,CAAC;IACTF,eAAe,EAAE,SAAS;IAC1BY,OAAO,EAAE;EACb,CAAC;EACDnB,mBAAmB,EAAE;IACjBG,IAAI,EAAE,CAAC;IACPiB,UAAU,EAAE,EAAE;IACdC,aAAa,EAAE;EACnB;AACJ,CAAC,CAAC"}