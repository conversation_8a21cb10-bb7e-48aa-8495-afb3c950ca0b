{"version": 3, "names": ["ZegoUIKitPrebuiltVideoConference", "props", "appID", "appSign", "userID", "userName", "conferenceID", "config", "token", "onRequireNewToken", "audioVideoViewConfig", "turnOnCameraWhenJoining", "turnOnMicrophoneWhenJoining", "useSpeakerWhenJoining", "bottomMenuBarConfig", "topMenuBarConfig", "memberListConfig", "layout", "mode", "ZegoLayoutMode", "gallery", "addBorderRadiusAndSpacingBetweenView", "leaveConfirmDialogInfo", "onLeave", "onLeaveConfirmation", "showMicrophoneStateOnView", "showCameraStateOnView", "showUserNameOnView", "showSoundWavesInAudioMode", "useVideoViewAspectFill", "foregroundBuilder", "buttons", "ZegoMenuBarButtonName", "toggleCameraButton", "switchCameraButton", "leaveButton", "toggleMicrophoneButton", "switchAudioOutputButton", "maxCount", "extendButtons", "hideAutomatically", "hideByClick", "style", "ZegoMenuBarStyle", "dark", "isVisible", "title", "topTitle", "topButtons", "showMemberListButton", "topMaxCount", "topExtendButtons", "topHideAutomatically", "topHideByClick", "topStyle", "showMicrophoneState", "showCameraState", "itemBuilder", "isMenubarVisable", "setIsMenubarVidable", "useState", "isTopMenubarVisable", "setTopIsMenubarVidable", "isCallMemberListVisable", "setIsCallMemberListVisable", "hideCountdown", "hideCountdownOnTopMenu", "onFullPageTouch", "grantPermissions", "callback", "Platform", "OS", "grantedAudio", "PermissionsAndroid", "check", "PERMISSIONS", "RECORD_AUDIO", "grantedCamera", "CAMERA", "ungrantedPermissions", "isAudioGranted", "isVideoGranted", "push", "error", "requestMultiple", "then", "data", "console", "warn", "showLeaveAlert", "Promise", "resolve", "reject", "message", "cancelButtonName", "confirmButtonName", "<PERSON><PERSON>", "alert", "text", "onPress", "cancelable", "useEffect", "callbackID", "String", "Math", "floor", "random", "ZegoUIKit", "init", "turnCameraOn", "turnMicrophoneOn", "setAudioOutputToSpeaker", "joinRoom", "leaveRoom", "useInterval", "delay", "savedCallback", "useRef", "current", "tick", "id", "setInterval", "clearInterval", "onOpenCallMemberList", "onCloseCallMemberList", "styles", "container", "fillParent", "audioVideoView", "userInfo", "StyleSheet", "create", "flex", "alignItems", "justifyContent", "zIndex", "width", "height", "position", "right", "top"], "sources": ["index.js"], "sourcesContent": ["import React, { useEffect, useState, useRef } from 'react';\nimport { PermissionsAndroid, Alert } from 'react-native';\n\nimport { StyleSheet, View } from 'react-native';\nimport ZegoUIKit, { ZegoAudioVideoContainer, ZegoLayoutMode } from '@zegocloud/zego-uikit-rn'\nimport AudioVideoForegroundView from './AudioVideoForegroundView';\nimport ZegoBottomBar from './ZegoBottomBar';\nimport ZegoTopMenuBar from './ZegoTopMenuBar';\nimport ZegoCallMemberList from './ZegoCallMemberList';\nimport ZegoMenuBarButtonName from './ZegoMenuBarButtonName';\nimport ZegoMenuBarStyle from './ZegoMenuBarStyle';\n\n\nexport default function ZegoUIKitPrebuiltVideoConference(props) {\n    const {\n        appID,\n        appSign,\n        userID,\n        userName,\n        conferenceID,\n        config,\n        token,\n        onRequireNewToken,\n    } = props;\n    const {\n        audioVideoViewConfig = {},\n\n        turnOnCameraWhenJoining = true,\n        turnOnMicrophoneWhenJoining = true,\n        useSpeakerWhenJoining = true,\n\n        bottomMenuBarConfig = {},\n        topMenuBarConfig = {},\n        memberListConfig = {},\n\n        layout = { \n            mode: ZegoLayoutMode.gallery,\n            config: {\n                addBorderRadiusAndSpacingBetweenView: false\n            }\n        },\n\n        leaveConfirmDialogInfo, // {title: '', cancelButtonName: '', confirmButtonName: ''}\n\n        onLeave,\n        onLeaveConfirmation,\n    } = config;\n    const {\n        showMicrophoneStateOnView = true,\n        showCameraStateOnView = false,\n        showUserNameOnView = true,\n        showSoundWavesInAudioMode = true,\n        useVideoViewAspectFill = false,\n        foregroundBuilder,\n    } = audioVideoViewConfig;\n    const {\n        buttons = [\n            ZegoMenuBarButtonName.toggleCameraButton,\n            ZegoMenuBarButtonName.switchCameraButton,\n            ZegoMenuBarButtonName.leaveButton,\n            ZegoMenuBarButtonName.toggleMicrophoneButton,\n            ZegoMenuBarButtonName.switchAudioOutputButton\n        ],\n        maxCount = 5,\n        extendButtons = [],\n        hideAutomatically = true,\n        hideByClick = true,\n        style = ZegoMenuBarStyle.dark,\n    } = bottomMenuBarConfig;\n    const {\n        isVisible = true,\n        title:topTitle = 'Meeting',\n        buttons:topButtons = [ ZegoMenuBarButtonName.showMemberListButton ],\n        maxCount:topMaxCount = 3,\n        extendButtons:topExtendButtons = [],\n        hideAutomatically:topHideAutomatically = true,\n        hideByClick:topHideByClick = true,\n        style:topStyle = ZegoMenuBarStyle.dark,\n    } = topMenuBarConfig;\n    const {\n        showMicrophoneState = true,\n        showCameraState = true,\n        itemBuilder,\n    } = memberListConfig;\n\n    const [isMenubarVisable, setIsMenubarVidable] = useState(true);\n    const [isTopMenubarVisable, setTopIsMenubarVidable] = useState(true);\n    const [isCallMemberListVisable, setIsCallMemberListVisable] = useState(false);\n    var hideCountdown = 5;\n    var hideCountdownOnTopMenu = 5;\n\n    const onFullPageTouch = () => {\n        hideCountdown = 5;\n        hideCountdownOnTopMenu = 5;\n        if (isMenubarVisable) {\n            if (hideByClick) {\n                setIsMenubarVidable(false);\n                setIsCallMemberListVisable(false);\n            }\n        } else {\n            setIsMenubarVidable(true);\n        }\n        if (isTopMenubarVisable) {\n            if (topHideByClick) {\n                setTopIsMenubarVidable(false);\n                setIsCallMemberListVisable(false);\n            }\n        } else {\n            setTopIsMenubarVidable(true);\n        }\n    }\n    const grantPermissions = async (callback) => {\n        // Android: Dynamically obtaining device permissions\n        if (Platform.OS === 'android') {\n            // Check if permission granted\n            let grantedAudio = PermissionsAndroid.check(\n                PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,\n            );\n            let grantedCamera = PermissionsAndroid.check(\n                PermissionsAndroid.PERMISSIONS.CAMERA,\n            );\n            const ungrantedPermissions = [];\n            try {\n                const isAudioGranted = await grantedAudio;\n                const isVideoGranted = await grantedCamera;\n                if (!isAudioGranted) {\n                    ungrantedPermissions.push(\n                        PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,\n                    );\n                }\n                if (!isVideoGranted) {\n                    ungrantedPermissions.push(PermissionsAndroid.PERMISSIONS.CAMERA);\n                }\n            } catch (error) {\n                ungrantedPermissions.push(\n                    PermissionsAndroid.PERMISSIONS.RECORD_AUDIO,\n                    PermissionsAndroid.PERMISSIONS.CAMERA,\n                );\n            }\n            // If not, request it\n            return PermissionsAndroid.requestMultiple(ungrantedPermissions).then(\n                data => {\n                    console.warn('requestMultiple', data);\n                    if (callback) {\n                        callback();\n                    }\n                },\n            );\n        } else if (callback) {\n            callback();\n        }\n    }\n    // Default operation for click the leave button\n    const showLeaveAlert = () => {\n        return new Promise((resolve, reject) => {\n            if (leaveConfirmDialogInfo) {\n                const {\n                    title = \"Leave the call\",\n                    message = \"Are you sure to leave the call?\",\n                    cancelButtonName = \"Cancel\",\n                    confirmButtonName = \"Confirm\"\n                } = leaveConfirmDialogInfo;\n                Alert.alert(\n                    title,\n                    message,\n                    [\n                        {\n                            text: cancelButtonName,\n                            onPress: () => {\n                                reject();\n                            },\n                            style: \"cancel\",\n                        },\n                        {\n                            text: confirmButtonName,\n                            onPress: () => {\n                                resolve();\n                            },\n                        },\n                    ],\n                    {\n                        cancelable: false,\n                    }\n                );\n            } else {\n                resolve();\n            }\n        });\n    }\n\n    useEffect(() => {\n        const callbackID = 'ZegoUIKitPrebuiltVideoConference' + String(Math.floor(Math.random() * 10000));\n        ZegoUIKit.onRequireNewToken(callbackID, onRequireNewToken);\n        return () => {\n            ZegoUIKit.onRequireNewToken(callbackID);\n        }\n    }, []);\n    useEffect(() => {\n        ZegoUIKit.init(\n            appID,\n            appSign,\n            { userID: userID, userName: userName }).then(() => {\n\n                ZegoUIKit.turnCameraOn('', turnOnCameraWhenJoining);\n                ZegoUIKit.turnMicrophoneOn('', turnOnMicrophoneWhenJoining);\n                ZegoUIKit.setAudioOutputToSpeaker(useSpeakerWhenJoining);\n\n                grantPermissions(() => {\n                    if (appSign) {\n                        ZegoUIKit.joinRoom(conferenceID);\n                    } else {\n                        ZegoUIKit.joinRoom(conferenceID, token || onRequireNewToken());\n                    }\n                });\n\n            });\n\n        return () => {\n            ZegoUIKit.leaveRoom();\n        }\n    }, []);\n\n    function useInterval(callback, delay) {\n        const savedCallback = useRef();\n\n        useEffect(() => {\n            savedCallback.current = callback;\n        });\n\n        useEffect(() => {\n            function tick() {\n                savedCallback.current();\n            }\n            if (delay !== null) {\n                let id = setInterval(tick, delay);\n                return () => clearInterval(id);\n            }\n        }, [delay]);\n    }\n\n    function onOpenCallMemberList() {\n        setIsCallMemberListVisable(true);\n    }\n\n    function onCloseCallMemberList() {\n        setIsCallMemberListVisable(false);\n    }\n\n    useInterval(() => {\n        hideCountdown--;\n        if (hideCountdown <= 0) {\n            hideCountdown = 5;\n            if (hideAutomatically) {\n                setIsMenubarVidable(false);\n            }\n        }\n    }, 1000);\n\n    useInterval(() => {\n        hideCountdownOnTopMenu--;\n        if (hideCountdownOnTopMenu <= 0) {\n            hideCountdownOnTopMenu = 5;\n            if (topHideAutomatically) {\n                setTopIsMenubarVidable(false);\n            }\n        }\n    }, 1000);\n\n    return (\n        <View style={[styles.container, styles.fillParent]} >\n            {isVisible && isTopMenubarVisable ?\n                <ZegoTopMenuBar\n                    menuTitle={topTitle}\n                    menuBarButtonsMaxCount={topMaxCount}\n                    menuBarButtons={topButtons}\n                    menuBarExtendedButtons={topExtendButtons}\n                    onLeave={onLeave}\n                    onLeaveConfirmation={onLeaveConfirmation ? onLeaveConfirmation : showLeaveAlert}\n                    turnOnCameraWhenJoining={turnOnCameraWhenJoining}\n                    turnOnMicrophoneWhenJoining={turnOnMicrophoneWhenJoining}\n                    useSpeakerWhenJoining={useSpeakerWhenJoining}\n                    onOpenCallMemberList={onOpenCallMemberList}\n                /> : <View />\n            }\n            <View style={styles.fillParent} pointerEvents='auto' onTouchStart={onFullPageTouch}>\n                <ZegoAudioVideoContainer style={[styles.audioVideoView, styles.fillParent]}\n                    audioVideoConfig={{\n                        showSoundWavesInAudioMode: showSoundWavesInAudioMode,\n                        useVideoViewAspectFill: useVideoViewAspectFill,\n                    }}\n                    layout={layout}\n                    foregroundBuilder={foregroundBuilder ? foregroundBuilder : ({ userInfo }) =>\n                        <AudioVideoForegroundView\n                            userInfo={userInfo}\n                            showMicrophoneStateOnView={showMicrophoneStateOnView}\n                            showCameraStateOnView={showCameraStateOnView}\n                            showUserNameOnView={showUserNameOnView}\n                        />\n                    }\n                />\n            </View>\n            {isMenubarVisable ?\n                <ZegoBottomBar\n                    menuBarButtonsMaxCount={maxCount}\n                    menuBarButtons={buttons}\n                    menuBarExtendedButtons={extendButtons}\n                    onLeave={onLeave}\n                    onLeaveConfirmation={onLeaveConfirmation ? onLeaveConfirmation : showLeaveAlert}\n                    turnOnCameraWhenJoining={turnOnCameraWhenJoining}\n                    turnOnMicrophoneWhenJoining={turnOnMicrophoneWhenJoining}\n                    useSpeakerWhenJoining={useSpeakerWhenJoining}\n                    onMorePress={() => { hideCountdown = 5; }}\n                /> :\n                <View />\n            }\n            {isCallMemberListVisable ?\n                <ZegoCallMemberList\n                    showMicrophoneState={showMicrophoneState}\n                    showCameraState={showCameraState}\n                    itemBuilder={itemBuilder}\n                    onCloseCallMemberList={onCloseCallMemberList}\n                /> :\n                <View />\n            }\n        </View>\n    );\n}\n\nconst styles = StyleSheet.create({\n    container: {\n        flex: 1,\n        alignItems: 'center',\n        justifyContent: 'center',\n        zIndex: 0,\n    },\n    fillParent: {\n        width: '100%',\n        height: '100%',\n        position: 'absolute',\n    },\n    audioVideoView: {\n        flex: 1,\n        zIndex: 2,\n        right: 0,\n        top: 0,\n    },\n});\n"], "mappings": ";;;;;;AAAA;AACA;AAGA;AACA;AACA;AACA;AACA;AACA;AACA;AAAkD;AAAA;AAAA;AAGnC,SAASA,gCAAgC,CAACC,KAAK,EAAE;EAC5D,MAAM;IACFC,KAAK;IACLC,OAAO;IACPC,MAAM;IACNC,QAAQ;IACRC,YAAY;IACZC,MAAM;IACNC,KAAK;IACLC;EACJ,CAAC,GAAGR,KAAK;EACT,MAAM;IACFS,oBAAoB,GAAG,CAAC,CAAC;IAEzBC,uBAAuB,GAAG,IAAI;IAC9BC,2BAA2B,GAAG,IAAI;IAClCC,qBAAqB,GAAG,IAAI;IAE5BC,mBAAmB,GAAG,CAAC,CAAC;IACxBC,gBAAgB,GAAG,CAAC,CAAC;IACrBC,gBAAgB,GAAG,CAAC,CAAC;IAErBC,MAAM,GAAG;MACLC,IAAI,EAAEC,2BAAc,CAACC,OAAO;MAC5Bb,MAAM,EAAE;QACJc,oCAAoC,EAAE;MAC1C;IACJ,CAAC;IAEDC,sBAAsB;IAAE;;IAExBC,OAAO;IACPC;EACJ,CAAC,GAAGjB,MAAM;EACV,MAAM;IACFkB,yBAAyB,GAAG,IAAI;IAChCC,qBAAqB,GAAG,KAAK;IAC7BC,kBAAkB,GAAG,IAAI;IACzBC,yBAAyB,GAAG,IAAI;IAChCC,sBAAsB,GAAG,KAAK;IAC9BC;EACJ,CAAC,GAAGpB,oBAAoB;EACxB,MAAM;IACFqB,OAAO,GAAG,CACNC,8BAAqB,CAACC,kBAAkB,EACxCD,8BAAqB,CAACE,kBAAkB,EACxCF,8BAAqB,CAACG,WAAW,EACjCH,8BAAqB,CAACI,sBAAsB,EAC5CJ,8BAAqB,CAACK,uBAAuB,CAChD;IACDC,QAAQ,GAAG,CAAC;IACZC,aAAa,GAAG,EAAE;IAClBC,iBAAiB,GAAG,IAAI;IACxBC,WAAW,GAAG,IAAI;IAClBC,KAAK,GAAGC,yBAAgB,CAACC;EAC7B,CAAC,GAAG9B,mBAAmB;EACvB,MAAM;IACF+B,SAAS,GAAG,IAAI;IAChBC,KAAK,EAACC,QAAQ,GAAG,SAAS;IAC1BhB,OAAO,EAACiB,UAAU,GAAG,CAAEhB,8BAAqB,CAACiB,oBAAoB,CAAE;IACnEX,QAAQ,EAACY,WAAW,GAAG,CAAC;IACxBX,aAAa,EAACY,gBAAgB,GAAG,EAAE;IACnCX,iBAAiB,EAACY,oBAAoB,GAAG,IAAI;IAC7CX,WAAW,EAACY,cAAc,GAAG,IAAI;IACjCX,KAAK,EAACY,QAAQ,GAAGX,yBAAgB,CAACC;EACtC,CAAC,GAAG7B,gBAAgB;EACpB,MAAM;IACFwC,mBAAmB,GAAG,IAAI;IAC1BC,eAAe,GAAG,IAAI;IACtBC;EACJ,CAAC,GAAGzC,gBAAgB;EAEpB,MAAM,CAAC0C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG,IAAAC,eAAQ,EAAC,IAAI,CAAC;EAC9D,MAAM,CAACC,mBAAmB,EAAEC,sBAAsB,CAAC,GAAG,IAAAF,eAAQ,EAAC,IAAI,CAAC;EACpE,MAAM,CAACG,uBAAuB,EAAEC,0BAA0B,CAAC,GAAG,IAAAJ,eAAQ,EAAC,KAAK,CAAC;EAC7E,IAAIK,aAAa,GAAG,CAAC;EACrB,IAAIC,sBAAsB,GAAG,CAAC;EAE9B,MAAMC,eAAe,GAAG,MAAM;IAC1BF,aAAa,GAAG,CAAC;IACjBC,sBAAsB,GAAG,CAAC;IAC1B,IAAIR,gBAAgB,EAAE;MAClB,IAAIjB,WAAW,EAAE;QACbkB,mBAAmB,CAAC,KAAK,CAAC;QAC1BK,0BAA0B,CAAC,KAAK,CAAC;MACrC;IACJ,CAAC,MAAM;MACHL,mBAAmB,CAAC,IAAI,CAAC;IAC7B;IACA,IAAIE,mBAAmB,EAAE;MACrB,IAAIR,cAAc,EAAE;QAChBS,sBAAsB,CAAC,KAAK,CAAC;QAC7BE,0BAA0B,CAAC,KAAK,CAAC;MACrC;IACJ,CAAC,MAAM;MACHF,sBAAsB,CAAC,IAAI,CAAC;IAChC;EACJ,CAAC;EACD,MAAMM,gBAAgB,GAAG,MAAOC,QAAQ,IAAK;IACzC;IACA,IAAIC,QAAQ,CAACC,EAAE,KAAK,SAAS,EAAE;MAC3B;MACA,IAAIC,YAAY,GAAGC,+BAAkB,CAACC,KAAK,CACvCD,+BAAkB,CAACE,WAAW,CAACC,YAAY,CAC9C;MACD,IAAIC,aAAa,GAAGJ,+BAAkB,CAACC,KAAK,CACxCD,+BAAkB,CAACE,WAAW,CAACG,MAAM,CACxC;MACD,MAAMC,oBAAoB,GAAG,EAAE;MAC/B,IAAI;QACA,MAAMC,cAAc,GAAG,MAAMR,YAAY;QACzC,MAAMS,cAAc,GAAG,MAAMJ,aAAa;QAC1C,IAAI,CAACG,cAAc,EAAE;UACjBD,oBAAoB,CAACG,IAAI,CACrBT,+BAAkB,CAACE,WAAW,CAACC,YAAY,CAC9C;QACL;QACA,IAAI,CAACK,cAAc,EAAE;UACjBF,oBAAoB,CAACG,IAAI,CAACT,+BAAkB,CAACE,WAAW,CAACG,MAAM,CAAC;QACpE;MACJ,CAAC,CAAC,OAAOK,KAAK,EAAE;QACZJ,oBAAoB,CAACG,IAAI,CACrBT,+BAAkB,CAACE,WAAW,CAACC,YAAY,EAC3CH,+BAAkB,CAACE,WAAW,CAACG,MAAM,CACxC;MACL;MACA;MACA,OAAOL,+BAAkB,CAACW,eAAe,CAACL,oBAAoB,CAAC,CAACM,IAAI,CAChEC,IAAI,IAAI;QACJC,OAAO,CAACC,IAAI,CAAC,iBAAiB,EAAEF,IAAI,CAAC;QACrC,IAAIjB,QAAQ,EAAE;UACVA,QAAQ,EAAE;QACd;MACJ,CAAC,CACJ;IACL,CAAC,MAAM,IAAIA,QAAQ,EAAE;MACjBA,QAAQ,EAAE;IACd;EACJ,CAAC;EACD;EACA,MAAMoB,cAAc,GAAG,MAAM;IACzB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAK;MACpC,IAAItE,sBAAsB,EAAE;QACxB,MAAM;UACFwB,KAAK,GAAG,gBAAgB;UACxB+C,OAAO,GAAG,iCAAiC;UAC3CC,gBAAgB,GAAG,QAAQ;UAC3BC,iBAAiB,GAAG;QACxB,CAAC,GAAGzE,sBAAsB;QAC1B0E,kBAAK,CAACC,KAAK,CACPnD,KAAK,EACL+C,OAAO,EACP,CACI;UACIK,IAAI,EAAEJ,gBAAgB;UACtBK,OAAO,EAAE,MAAM;YACXP,MAAM,EAAE;UACZ,CAAC;UACDlD,KAAK,EAAE;QACX,CAAC,EACD;UACIwD,IAAI,EAAEH,iBAAiB;UACvBI,OAAO,EAAE,MAAM;YACXR,OAAO,EAAE;UACb;QACJ,CAAC,CACJ,EACD;UACIS,UAAU,EAAE;QAChB,CAAC,CACJ;MACL,CAAC,MAAM;QACHT,OAAO,EAAE;MACb;IACJ,CAAC,CAAC;EACN,CAAC;EAED,IAAAU,gBAAS,EAAC,MAAM;IACZ,MAAMC,UAAU,GAAG,kCAAkC,GAAGC,MAAM,CAACC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,EAAE,GAAG,KAAK,CAAC,CAAC;IACjGC,oBAAS,CAAClG,iBAAiB,CAAC6F,UAAU,EAAE7F,iBAAiB,CAAC;IAC1D,OAAO,MAAM;MACTkG,oBAAS,CAAClG,iBAAiB,CAAC6F,UAAU,CAAC;IAC3C,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EACN,IAAAD,gBAAS,EAAC,MAAM;IACZM,oBAAS,CAACC,IAAI,CACV1G,KAAK,EACLC,OAAO,EACP;MAAEC,MAAM,EAAEA,MAAM;MAAEC,QAAQ,EAAEA;IAAS,CAAC,CAAC,CAACgF,IAAI,CAAC,MAAM;MAE/CsB,oBAAS,CAACE,YAAY,CAAC,EAAE,EAAElG,uBAAuB,CAAC;MACnDgG,oBAAS,CAACG,gBAAgB,CAAC,EAAE,EAAElG,2BAA2B,CAAC;MAC3D+F,oBAAS,CAACI,uBAAuB,CAAClG,qBAAqB,CAAC;MAExDuD,gBAAgB,CAAC,MAAM;QACnB,IAAIjE,OAAO,EAAE;UACTwG,oBAAS,CAACK,QAAQ,CAAC1G,YAAY,CAAC;QACpC,CAAC,MAAM;UACHqG,oBAAS,CAACK,QAAQ,CAAC1G,YAAY,EAAEE,KAAK,IAAIC,iBAAiB,EAAE,CAAC;QAClE;MACJ,CAAC,CAAC;IAEN,CAAC,CAAC;IAEN,OAAO,MAAM;MACTkG,oBAAS,CAACM,SAAS,EAAE;IACzB,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,SAASC,WAAW,CAAC7C,QAAQ,EAAE8C,KAAK,EAAE;IAClC,MAAMC,aAAa,GAAG,IAAAC,aAAM,GAAE;IAE9B,IAAAhB,gBAAS,EAAC,MAAM;MACZe,aAAa,CAACE,OAAO,GAAGjD,QAAQ;IACpC,CAAC,CAAC;IAEF,IAAAgC,gBAAS,EAAC,MAAM;MACZ,SAASkB,IAAI,GAAG;QACZH,aAAa,CAACE,OAAO,EAAE;MAC3B;MACA,IAAIH,KAAK,KAAK,IAAI,EAAE;QAChB,IAAIK,EAAE,GAAGC,WAAW,CAACF,IAAI,EAAEJ,KAAK,CAAC;QACjC,OAAO,MAAMO,aAAa,CAACF,EAAE,CAAC;MAClC;IACJ,CAAC,EAAE,CAACL,KAAK,CAAC,CAAC;EACf;EAEA,SAASQ,oBAAoB,GAAG;IAC5B3D,0BAA0B,CAAC,IAAI,CAAC;EACpC;EAEA,SAAS4D,qBAAqB,GAAG;IAC7B5D,0BAA0B,CAAC,KAAK,CAAC;EACrC;EAEAkD,WAAW,CAAC,MAAM;IACdjD,aAAa,EAAE;IACf,IAAIA,aAAa,IAAI,CAAC,EAAE;MACpBA,aAAa,GAAG,CAAC;MACjB,IAAIzB,iBAAiB,EAAE;QACnBmB,mBAAmB,CAAC,KAAK,CAAC;MAC9B;IACJ;EACJ,CAAC,EAAE,IAAI,CAAC;EAERuD,WAAW,CAAC,MAAM;IACdhD,sBAAsB,EAAE;IACxB,IAAIA,sBAAsB,IAAI,CAAC,EAAE;MAC7BA,sBAAsB,GAAG,CAAC;MAC1B,IAAId,oBAAoB,EAAE;QACtBU,sBAAsB,CAAC,KAAK,CAAC;MACjC;IACJ;EACJ,CAAC,EAAE,IAAI,CAAC;EAER,oBACI,6BAAC,iBAAI;IAAC,KAAK,EAAE,CAAC+D,MAAM,CAACC,SAAS,EAAED,MAAM,CAACE,UAAU;EAAE,GAC9ClF,SAAS,IAAIgB,mBAAmB,gBAC7B,6BAAC,uBAAc;IACX,SAAS,EAAEd,QAAS;IACpB,sBAAsB,EAAEG,WAAY;IACpC,cAAc,EAAEF,UAAW;IAC3B,sBAAsB,EAAEG,gBAAiB;IACzC,OAAO,EAAE5B,OAAQ;IACjB,mBAAmB,EAAEC,mBAAmB,GAAGA,mBAAmB,GAAGiE,cAAe;IAChF,uBAAuB,EAAE9E,uBAAwB;IACjD,2BAA2B,EAAEC,2BAA4B;IACzD,qBAAqB,EAAEC,qBAAsB;IAC7C,oBAAoB,EAAE8G;EAAqB,EAC7C,gBAAG,6BAAC,iBAAI,OAAG,eAEjB,6BAAC,iBAAI;IAAC,KAAK,EAAEE,MAAM,CAACE,UAAW;IAAC,aAAa,EAAC,MAAM;IAAC,YAAY,EAAE5D;EAAgB,gBAC/E,6BAAC,oCAAuB;IAAC,KAAK,EAAE,CAAC0D,MAAM,CAACG,cAAc,EAAEH,MAAM,CAACE,UAAU,CAAE;IACvE,gBAAgB,EAAE;MACdnG,yBAAyB,EAAEA,yBAAyB;MACpDC,sBAAsB,EAAEA;IAC5B,CAAE;IACF,MAAM,EAAEZ,MAAO;IACf,iBAAiB,EAAEa,iBAAiB,GAAGA,iBAAiB,GAAG;MAAA,IAAC;QAAEmG;MAAS,CAAC;MAAA,oBACpE,6BAAC,iCAAwB;QACrB,QAAQ,EAAEA,QAAS;QACnB,yBAAyB,EAAExG,yBAA0B;QACrD,qBAAqB,EAAEC,qBAAsB;QAC7C,kBAAkB,EAAEC;MAAmB,EACzC;IAAA;EACL,EACH,CACC,EACN+B,gBAAgB,gBACb,6BAAC,sBAAa;IACV,sBAAsB,EAAEpB,QAAS;IACjC,cAAc,EAAEP,OAAQ;IACxB,sBAAsB,EAAEQ,aAAc;IACtC,OAAO,EAAEhB,OAAQ;IACjB,mBAAmB,EAAEC,mBAAmB,GAAGA,mBAAmB,GAAGiE,cAAe;IAChF,uBAAuB,EAAE9E,uBAAwB;IACjD,2BAA2B,EAAEC,2BAA4B;IACzD,qBAAqB,EAAEC,qBAAsB;IAC7C,WAAW,EAAE,MAAM;MAAEoD,aAAa,GAAG,CAAC;IAAE;EAAE,EAC5C,gBACF,6BAAC,iBAAI,OAAG,EAEXF,uBAAuB,gBACpB,6BAAC,2BAAkB;IACf,mBAAmB,EAAER,mBAAoB;IACzC,eAAe,EAAEC,eAAgB;IACjC,WAAW,EAAEC,WAAY;IACzB,qBAAqB,EAAEmE;EAAsB,EAC/C,gBACF,6BAAC,iBAAI,OAAG,CAET;AAEf;AAEA,MAAMC,MAAM,GAAGK,uBAAU,CAACC,MAAM,CAAC;EAC7BL,SAAS,EAAE;IACPM,IAAI,EAAE,CAAC;IACPC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,MAAM,EAAE;EACZ,CAAC;EACDR,UAAU,EAAE;IACRS,KAAK,EAAE,MAAM;IACbC,MAAM,EAAE,MAAM;IACdC,QAAQ,EAAE;EACd,CAAC;EACDV,cAAc,EAAE;IACZI,IAAI,EAAE,CAAC;IACPG,MAAM,EAAE,CAAC;IACTI,KAAK,EAAE,CAAC;IACRC,GAAG,EAAE;EACT;AACJ,CAAC,CAAC"}