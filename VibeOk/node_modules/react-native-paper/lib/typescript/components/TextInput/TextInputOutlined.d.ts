import * as React from 'react';
import type { ChildTextInputProps } from './types';
declare const TextInputOutlined: ({ disabled, editable, label, error, selectionColor: customSelectionColor, cursorColor, underlineColor: _underlineColor, outlineColor: customOutlineColor, activeOutlineColor, outlineStyle, textColor, dense, style, theme, render, multiline, parentState, innerRef, onFocus, forceFocus, onBlur, onChangeText, onLayoutAnimatedText, onLabelTextLayout, onLeftAffixLayoutChange, onRightAffixLayoutChange, onInputLayout, onLayout, left, right, placeholderTextColor, testID, contentStyle, scaledLabel, ...rest }: ChildTextInputProps) => React.JSX.Element;
export default TextInputOutlined;
//# sourceMappingURL=TextInputOutlined.d.ts.map