import * as React from 'react';
import { Animated, ScrollViewProps, StyleProp, ViewStyle } from 'react-native';
import type { MD3Elevation, ThemeProp } from '../../types';
import { ElevationLevels } from '../../types';
export type Props = {
    /**
     * Whether the Menu is currently visible.
     */
    visible: boolean;
    /**
     * The anchor to open the menu from. In most cases, it will be a button that opens the menu.
     */
    anchor: React.ReactNode | {
        x: number;
        y: number;
    };
    /**
     * Whether the menu should open at the top of the anchor or at its bottom.
     * Applied only when anchor is a node, not an x/y position.
     */
    anchorPosition?: 'top' | 'bottom';
    /**
     * Extra margin to add at the top of the menu to account for translucent status bar on Android.
     * If you are using Expo, we assume translucent status bar and set a height for status bar automatically.
     * Pass `0` or a custom value to and customize it.
     * This is automatically handled on iOS.
     */
    statusBarHeight?: number;
    /**
     * Callback called when <PERSON><PERSON> is dismissed. The `visible` prop needs to be updated when this is called.
     */
    onDismiss?: () => void;
    /**
     * Accessibility label for the overlay. This is read by the screen reader when the user taps outside the menu.
     */
    overlayAccessibilityLabel?: string;
    /**
     * Content of the `Menu`.
     */
    children: React.ReactNode;
    /**
     * Style of menu's inner content.
     */
    contentStyle?: Animated.WithAnimatedValue<StyleProp<ViewStyle>>;
    style?: StyleProp<ViewStyle>;
    /**
     * Elevation level of the menu's content. Shadow styles are calculated based on this value. Default `backgroundColor` is taken from the corresponding `theme.colors.elevation` property. By default equals `2`.
     * @supported Available in v5.x with theme version 3
     */
    elevation?: MD3Elevation;
    /**
     * Mode of the menu's content.
     * - `elevated` - Surface with a shadow and background color corresponding to set `elevation` value.
     * - `flat` - Surface without a shadow, with the background color corresponding to set `elevation` value.
     *
     * @supported Available in v5.x with theme version 3
     */
    mode?: 'flat' | 'elevated';
    /**
     * @optional
     */
    theme?: ThemeProp;
    /**
     * Inner ScrollView prop
     */
    keyboardShouldPersistTaps?: ScrollViewProps['keyboardShouldPersistTaps'];
    /**
     * testID to be used on tests.
     */
    testID?: string;
};
export declare const ELEVATION_LEVELS_MAP: ElevationLevels[];
/**
 * Menus display a list of choices on temporary elevated surfaces. Their placement varies based on the element that opens them.
 *
 * ## Usage
 * ```js
 * import * as React from 'react';
 * import { View } from 'react-native';
 * import { Button, Menu, Divider, PaperProvider } from 'react-native-paper';
 *
 * const MyComponent = () => {
 *   const [visible, setVisible] = React.useState(false);
 *
 *   const openMenu = () => setVisible(true);
 *
 *   const closeMenu = () => setVisible(false);
 *
 *   return (
 *     <PaperProvider>
 *       <View
 *         style={{
 *           paddingTop: 50,
 *           flexDirection: 'row',
 *           justifyContent: 'center',
 *         }}>
 *         <Menu
 *           visible={visible}
 *           onDismiss={closeMenu}
 *           anchor={<Button onPress={openMenu}>Show menu</Button>}>
 *           <Menu.Item onPress={() => {}} title="Item 1" />
 *           <Menu.Item onPress={() => {}} title="Item 2" />
 *           <Divider />
 *           <Menu.Item onPress={() => {}} title="Item 3" />
 *         </Menu>
 *       </View>
 *     </PaperProvider>
 *   );
 * };
 *
 * export default MyComponent;
 * ```
 *
 * ### Note
 * When using `Menu` within a React Native's `Modal` component, you need to wrap all
 * `Modal` contents within a `PaperProvider` in order for the menu to show. This
 * wrapping is not necessary if you use Paper's `Modal` instead.
 */
declare const Menu: {
    ({ visible, statusBarHeight, overlayAccessibilityLabel, testID, anchor, onDismiss, anchorPosition, contentStyle, style, elevation, mode, children, theme: themeOverrides, keyboardShouldPersistTaps, }: Props): React.JSX.Element;
    Item: {
        ({ leadingIcon, trailingIcon, dense, title, disabled, background, onPress, style, containerStyle, contentStyle, titleStyle, rippleColor: customRippleColor, testID, accessibilityLabel, accessibilityState, theme: themeOverrides, titleMaxFontSizeMultiplier, hitSlop, }: import("./MenuItem").Props): React.JSX.Element;
        displayName: string;
    };
};
export default Menu;
//# sourceMappingURL=Menu.d.ts.map