{"version": 3, "names": ["_createMaterialBottomTabNavigator", "_interopRequireDefault", "require", "_MaterialBottomTabView", "e", "__esModule", "default"], "sourceRoot": "../../../src", "sources": ["react-navigation/index.tsx"], "mappings": ";;;;;;;;;;;;;;;;;AAGA,IAAAA,iCAAA,GAAAC,sBAAA,CAAAC,OAAA;AAKA,IAAAC,sBAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAiF,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA", "ignoreList": []}