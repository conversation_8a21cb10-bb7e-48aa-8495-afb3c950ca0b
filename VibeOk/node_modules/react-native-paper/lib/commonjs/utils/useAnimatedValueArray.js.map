{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "useAnimatedValueArray", "initialValues", "refs", "useRef", "current", "length", "for<PERSON>ach", "initialValue", "Animated", "Value"], "sourceRoot": "../../../src", "sources": ["utils/useAnimatedValueArray.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAAwC,SAAAD,wBAAAG,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAL,uBAAA,YAAAA,CAAAG,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAEzB,SAASkB,qBAAqBA,CAACC,aAAuB,EAAE;EACrE,MAAMC,IAAI,GAAGzB,KAAK,CAAC0B,MAAM,CAAmB,EAAE,CAAC;EAE/CD,IAAI,CAACE,OAAO,CAACC,MAAM,GAAGJ,aAAa,CAACI,MAAM;EAC1CJ,aAAa,CAACK,OAAO,CAAC,CAACC,YAAY,EAAEnB,CAAC,KAAK;IACzCc,IAAI,CAACE,OAAO,CAAChB,CAAC,CAAC,GAAGc,IAAI,CAACE,OAAO,CAAChB,CAAC,CAAC,IAAI,IAAIoB,qBAAQ,CAACC,KAAK,CAACF,YAAY,CAAC;EACvE,CAAC,CAAC;EAEF,OAAOL,IAAI,CAACE,OAAO;AACrB", "ignoreList": []}