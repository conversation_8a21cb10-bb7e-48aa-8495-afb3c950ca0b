{"version": 3, "names": ["touchableEvents", "has<PERSON>ou<PERSON><PERSON><PERSON><PERSON>", "touchableEventObject", "some", "event", "Boolean"], "sourceRoot": "../../../src", "sources": ["utils/hasTouchHandler.tsx"], "mappings": ";;;;;;AAEA,MAAMA,eAAe,GAAG,CACtB,SAAS,EACT,aAAa,EACb,WAAW,EACX,YAAY,CACJ;AASK,SAASC,eAAeA,CACrCC,oBAA0C,EAC1C;EACA,OAAOF,eAAe,CAACG,IAAI,CAAEC,KAAK,IAAK;IACrC,OAAOC,OAAO,CAACH,oBAAoB,CAACE,KAAK,CAAC,CAAC;EAC7C,CAAC,CAAC;AACJ", "ignoreList": []}