{"version": 3, "names": ["React", "Animated", "useAnimatedValueArray", "initialValues", "refs", "useRef", "current", "length", "for<PERSON>ach", "initialValue", "i", "Value"], "sourceRoot": "../../../src", "sources": ["utils/useAnimatedValueArray.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,QAAQ,QAAQ,cAAc;AAEvC,eAAe,SAASC,qBAAqBA,CAACC,aAAuB,EAAE;EACrE,MAAMC,IAAI,GAAGJ,KAAK,CAACK,MAAM,CAAmB,EAAE,CAAC;EAE/CD,IAAI,CAACE,OAAO,CAACC,MAAM,GAAGJ,aAAa,CAACI,MAAM;EAC1CJ,aAAa,CAACK,OAAO,CAAC,CAACC,YAAY,EAAEC,CAAC,KAAK;IACzCN,IAAI,CAACE,OAAO,CAACI,CAAC,CAAC,GAAGN,IAAI,CAACE,OAAO,CAACI,CAAC,CAAC,IAAI,IAAIT,QAAQ,CAACU,KAAK,CAACF,YAAY,CAAC;EACvE,CAAC,CAAC;EAEF,OAAOL,IAAI,CAACE,OAAO;AACrB", "ignoreList": []}