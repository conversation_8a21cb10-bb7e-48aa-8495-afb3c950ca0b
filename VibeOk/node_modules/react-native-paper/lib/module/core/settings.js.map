{"version": 3, "names": ["React", "MaterialCommunityIcon", "SettingsContext", "createContext", "icon", "rippleEffectEnabled", "Provider", "Consumer"], "sourceRoot": "../../../src", "sources": ["core/settings.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,qBAAqB,MAErB,qCAAqC;AAa5C,OAAO,MAAMC,eAAe,gBAAGF,KAAK,CAACG,aAAa,CAAW;EAC3DC,IAAI,EAAEH,qBAAqB;EAC3BI,mBAAmB,EAAE;AACvB,CAAC,CAAC;AAEF,OAAO,MAAM;EAAEC,QAAQ;EAAEC;AAAS,CAAC,GAAGL,eAAe", "ignoreList": []}