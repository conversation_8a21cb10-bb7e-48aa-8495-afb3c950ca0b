{"version": 3, "names": ["Platform", "typescale", "fontConfig", "web", "regular", "fontFamily", "fontWeight", "medium", "light", "thin", "ios", "default", "configureV2Fonts", "config", "fonts", "select", "configureV3Fonts", "isFlatConfig", "Object", "keys", "every", "key", "fromEntries", "entries", "map", "variantName", "variantProperties", "assign", "configure<PERSON>onts", "params", "isV3"], "sourceRoot": "../../../src", "sources": ["styles/fonts.tsx"], "mappings": "AAAA,SAASA,QAAQ,QAAwB,cAAc;AAGvD,SAASC,SAAS,QAAQ,oBAAoB;AAE9C,OAAO,MAAMC,UAAU,GAAG;EACxBC,GAAG,EAAE;IACHC,OAAO,EAAE;MACPC,UAAU,EAAE,wDAAwD;MACpEC,UAAU,EAAE;IACd,CAAC;IACDC,MAAM,EAAE;MACNF,UAAU,EAAE,wDAAwD;MACpEC,UAAU,EAAE;IACd,CAAC;IACDE,KAAK,EAAE;MACLH,UAAU,EAAE,wDAAwD;MACpEC,UAAU,EAAE;IACd,CAAC;IACDG,IAAI,EAAE;MACJJ,UAAU,EAAE,wDAAwD;MACpEC,UAAU,EAAE;IACd;EACF,CAAC;EACDI,GAAG,EAAE;IACHN,OAAO,EAAE;MACPC,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAE;IACd,CAAC;IACDC,MAAM,EAAE;MACNF,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAE;IACd,CAAC;IACDE,KAAK,EAAE;MACLH,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAE;IACd,CAAC;IACDG,IAAI,EAAE;MACJJ,UAAU,EAAE,QAAQ;MACpBC,UAAU,EAAE;IACd;EACF,CAAC;EACDK,OAAO,EAAE;IACPP,OAAO,EAAE;MACPC,UAAU,EAAE,YAAY;MACxBC,UAAU,EAAE;IACd,CAAC;IACDC,MAAM,EAAE;MACNF,UAAU,EAAE,mBAAmB;MAC/BC,UAAU,EAAE;IACd,CAAC;IACDE,KAAK,EAAE;MACLH,UAAU,EAAE,kBAAkB;MAC9BC,UAAU,EAAE;IACd,CAAC;IACDG,IAAI,EAAE;MACJJ,UAAU,EAAE,iBAAiB;MAC7BC,UAAU,EAAE;IACd;EACF;AACF,CAAC;AAeD,SAASM,gBAAgBA,CAACC,MAAsB,EAAS;EACvD,MAAMC,KAAK,GAAGd,QAAQ,CAACe,MAAM,CAAC;IAAE,GAAGb,UAAU;IAAE,GAAGW;EAAO,CAAC,CAAU;EACpE,OAAOC,KAAK;AACd;AAEA,SAASE,gBAAgBA,CACvBH,MAAsB,EACsC;EAC5D,IAAI,CAACA,MAAM,EAAE;IACX,OAAOZ,SAAS;EAClB;EAEA,MAAMgB,YAAY,GAAGC,MAAM,CAACC,IAAI,CAACN,MAAM,CAAC,CAACO,KAAK,CAC3CC,GAAG,IAAK,OAAOR,MAAM,CAACQ,GAAG,CAAwB,KAAK,QACzD,CAAC;EAED,IAAIJ,YAAY,EAAE;IAChB,OAAOC,MAAM,CAACI,WAAW,CACvBJ,MAAM,CAACK,OAAO,CAACtB,SAAS,CAAC,CAACuB,GAAG,CAAC,CAAC,CAACC,WAAW,EAAEC,iBAAiB,CAAC,KAAK,CAClED,WAAW,EACX;MAAE,GAAGC,iBAAiB;MAAE,GAAGb;IAAO,CAAC,CACpC,CACH,CAAC;EACH;EAEA,OAAOK,MAAM,CAACS,MAAM,CAClB,CAAC,CAAC,EACF1B,SAAS,EACT,GAAGiB,MAAM,CAACK,OAAO,CAACV,MAAM,CAAC,CAACW,GAAG,CAAC,CAAC,CAACC,WAAW,EAAEC,iBAAiB,CAAC,MAAM;IACnE,CAACD,WAAW,GAAG;MACb,GAAGxB,SAAS,CAACwB,WAAW,CAAoB;MAC5C,GAAGC;IACL;EACF,CAAC,CAAC,CACJ,CAAC;AACH;;AAEA;;AAEA;;AAKA;;AAKA;;AAKA;;AAKA;AACA,eAAe,SAASE,cAAcA,CAACC,MAAY,EAAE;EACnD,MAAM;IAAEC,IAAI,GAAG,IAAI;IAAEjB;EAAO,CAAC,GAAGgB,MAAM,IAAI,CAAC,CAAC;EAE5C,IAAIC,IAAI,EAAE;IACR,OAAOd,gBAAgB,CAACH,MAAM,CAAC;EACjC;EACA,OAAOD,gBAAgB,CAACC,MAAM,CAAC;AACjC", "ignoreList": []}