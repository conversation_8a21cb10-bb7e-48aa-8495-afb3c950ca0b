{"version": 3, "names": ["React", "AdornmentSide", "AdornmentType", "InputMode", "TextInputAffix", "AffixAdornment", "TextInputIcon", "IconAdornment", "getConstants", "getAdornmentConfig", "left", "right", "adornmentConfig", "side", "Left", "adornment", "Right", "for<PERSON>ach", "isValidElement", "type", "Affix", "Icon", "push", "getAdornmentStyleAdjustmentForNativeInput", "leftAffixWidth", "rightAffix<PERSON>idth", "paddingHorizontal", "inputOffset", "mode", "isV3", "OUTLINED_INPUT_OFFSET", "ADORNMENT_OFFSET", "length", "adornmentStyleAdjustmentForNativeInput", "map", "isLeftSide", "inputModeAdornemntOffset", "Outlined", "paddingKey", "captalize", "affix<PERSON>id<PERSON>", "padding", "offset", "isAffix", "margin<PERSON>ey", "allStyleAdjustmentsMerged", "reduce", "mergedStyles", "currentStyle", "text", "char<PERSON>t", "toUpperCase", "slice", "TextInputAdornment", "onAffixChange", "textStyle", "visible", "topPosition", "isTextInputFocused", "forceFocus", "maxFontSizeMultiplier", "theme", "disabled", "createElement", "Fragment", "inputAdornmentComponent", "commonProps", "testID", "_extends", "key", "icon", "affix", "onLayout"], "sourceRoot": "../../../../../src", "sources": ["components/TextInput/Adornment/TextInputAdornment.tsx"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAWzB,SAASC,aAAa,EAAEC,aAAa,EAAEC,SAAS,QAAQ,SAAS;AACjE,OAAOC,cAAc,IAAIC,cAAc,QAAQ,kBAAkB;AACjE,OAAOC,aAAa,IAAIC,aAAa,QAAQ,iBAAiB;AAK9D,SAASC,YAAY,QAAQ,YAAY;AAEzC,OAAO,SAASC,kBAAkBA,CAAC;EACjCC,IAAI;EACJC;AAIF,CAAC,EAA0B;EACzB,IAAIC,eAAsB,GAAG,EAAE;EAC/B,IAAIF,IAAI,IAAIC,KAAK,EAAE;IACjB,CACE;MAAEE,IAAI,EAAEZ,aAAa,CAACa,IAAI;MAAEC,SAAS,EAAEL;IAAK,CAAC,EAC7C;MAAEG,IAAI,EAAEZ,aAAa,CAACe,KAAK;MAAED,SAAS,EAAEJ;IAAM,CAAC,CAChD,CAACM,OAAO,CAAC,CAAC;MAAEJ,IAAI;MAAEE;IAAU,CAAC,KAAK;MACjC,IAAIA,SAAS,iBAAIf,KAAK,CAACkB,cAAc,CAACH,SAAS,CAAC,EAAE;QAChD,IAAII,IAAI;QACR,IAAIJ,SAAS,CAACI,IAAI,KAAKf,cAAc,EAAE;UACrCe,IAAI,GAAGjB,aAAa,CAACkB,KAAK;QAC5B,CAAC,MAAM,IAAIL,SAAS,CAACI,IAAI,KAAKb,aAAa,EAAE;UAC3Ca,IAAI,GAAGjB,aAAa,CAACmB,IAAI;QAC3B;QACAT,eAAe,CAACU,IAAI,CAAC;UACnBT,IAAI;UACJM;QACF,CAAC,CAAC;MACJ;IACF,CAAC,CAAC;EACJ;EAEA,OAAOP,eAAe;AACxB;AAEA,OAAO,SAASW,yCAAyCA,CAAC;EACxDX,eAAe;EACfY,cAAc;EACdC,eAAe;EACfC,iBAAiB;EACjBC,WAAW,GAAG,CAAC;EACfC,IAAI;EACJC;AASF,CAAC,EAA+C;EAC9C,MAAM;IAAEC,qBAAqB;IAAEC;EAAiB,CAAC,GAAGvB,YAAY,CAACqB,IAAI,CAAC;EAEtE,IAAIjB,eAAe,CAACoB,MAAM,EAAE;IAC1B,MAAMC,sCAAsC,GAAGrB,eAAe,CAACsB,GAAG,CAChE,CAAC;MAAEf,IAAI;MAAEN;IAAsB,CAAC,KAAK;MACnC,MAAMsB,UAAU,GAAGtB,IAAI,KAAKZ,aAAa,CAACa,IAAI;MAC9C,MAAMsB,wBAAwB,GAC5BR,IAAI,KAAKzB,SAAS,CAACkC,QAAQ,GACvBN,gBAAgB,GAAGD,qBAAqB,GACxCC,gBAAgB;MACtB,MAAMO,UAAU,GAAG,UAAUC,SAAS,CAAC1B,IAAI,CAAC,EAAE;MAC9C,MAAM2B,UAAU,GAAGL,UAAU,GAAGX,cAAc,GAAGC,eAAe;MAChE,MAAMgB,OAAO,GACX,OAAOf,iBAAiB,KAAK,QAAQ,GACjCA,iBAAiB,GACjBU,wBAAwB;MAC9B,MAAMM,MAAM,GAAGF,UAAU,GAAGC,OAAO;MAEnC,MAAME,OAAO,GAAGxB,IAAI,KAAKjB,aAAa,CAACkB,KAAK;MAC5C,MAAMwB,SAAS,GAAG,SAASL,SAAS,CAAC1B,IAAI,CAAC,EAAE;MAE5C,OAAO;QACL,CAAC+B,SAAS,GAAGD,OAAO,GAAG,CAAC,GAAGD,MAAM;QACjC,CAACJ,UAAU,GAAGK,OAAO,GAAGD,MAAM,GAAGf;MACnC,CAAC;IACH,CACF,CAAC;IACD,MAAMkB,yBAAyB,GAC7BZ,sCAAsC,CAACa,MAAM,CAC3C,CAACC,YAAY,EAAEC,YAAY,KAAK;MAC9B,OAAO;QACL,GAAGD,YAAY;QACf,GAAGC;MACL,CAAC;IACH,CAAC,EACD,CAAC,CACH,CAAC;IACH,OAAOH,yBAAyB;EAClC,CAAC,MAAM;IACL,OAAO,CAAC,CAAC,CAAC,CAAC;EACb;AACF;AAEA,MAAMN,SAAS,GAAIU,IAAY,IAC7BA,IAAI,CAACC,MAAM,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,GAAGF,IAAI,CAACG,KAAK,CAAC,CAAC,CAAC;AA2B9C,MAAMC,kBAAoE,GAAGA,CAAC;EAC5EzC,eAAe;EACfF,IAAI;EACJC,KAAK;EACL2C,aAAa;EACbC,SAAS;EACTC,OAAO;EACPC,WAAW;EACXC,kBAAkB;EAClBC,UAAU;EACVjC,iBAAiB;EACjBkC,qBAAqB;EACrBC,KAAK;EACLC;AACF,CAAC,KAAK;EACJ,IAAIlD,eAAe,CAACoB,MAAM,EAAE;IAC1B,oBACEhC,KAAA,CAAA+D,aAAA,CAAA/D,KAAA,CAAAgE,QAAA,QACGpD,eAAe,CAACsB,GAAG,CAAC,CAAC;MAAEf,IAAI;MAAEN;IAAsB,CAAC,KAAK;MACxD,IAAIoD,uBAAuB;MAC3B,IAAIpD,IAAI,KAAKZ,aAAa,CAACa,IAAI,EAAE;QAC/BmD,uBAAuB,GAAGvD,IAAI;MAChC,CAAC,MAAM,IAAIG,IAAI,KAAKZ,aAAa,CAACe,KAAK,EAAE;QACvCiD,uBAAuB,GAAGtD,KAAK;MACjC;MAEA,MAAMuD,WAAW,GAAG;QAClBrD,IAAI,EAAEA,IAAI;QACVsD,MAAM,EAAE,GAAGtD,IAAI,IAAIM,IAAI,YAAY;QACnCuC,kBAAkB;QAClBhC,iBAAiB;QACjBoC;MACF,CAAC;MACD,IAAI3C,IAAI,KAAKjB,aAAa,CAACmB,IAAI,EAAE;QAC/B,oBACErB,KAAA,CAAA+D,aAAA,CAACxD,aAAa,EAAA6D,QAAA,KACRF,WAAW;UACfL,KAAK,EAAEA,KAAM;UACbQ,GAAG,EAAExD,IAAK;UACVyD,IAAI,EAAEL,uBAAwB;UAC9BR,WAAW,EAAEA,WAAW,CAACvD,aAAa,CAACmB,IAAI,CAAE;UAC7CsC,UAAU,EAAEA;QAAW,EACxB,CAAC;MAEN,CAAC,MAAM,IAAIxC,IAAI,KAAKjB,aAAa,CAACkB,KAAK,EAAE;QACvC,oBACEpB,KAAA,CAAA+D,aAAA,CAAC1D,cAAc,EAAA+D,QAAA,KACTF,WAAW;UACfG,GAAG,EAAExD,IAAK;UACV4C,WAAW,EAAEA,WAAW,CAACvD,aAAa,CAACkB,KAAK,CAAC,CAACP,IAAI,CAAE;UACpD0D,KAAK,EAAEN,uBAAwB;UAC/BV,SAAS,EAAEA,SAAU;UACrBiB,QAAQ,EAAElB,aAAa,CAACzC,IAAI,CAAE;UAC9B2C,OAAO,EAAEA,OAAQ;UACjBI,qBAAqB,EAAEA;QAAsB,EAC9C,CAAC;MAEN,CAAC,MAAM;QACL,OAAO,IAAI;MACb;IACF,CAAC,CACD,CAAC;EAEP,CAAC,MAAM;IACL,OAAO,IAAI;EACb;AACF,CAAC;AAED,eAAeP,kBAAkB", "ignoreList": []}