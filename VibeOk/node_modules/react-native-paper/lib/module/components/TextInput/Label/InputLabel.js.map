{"version": 3, "names": ["React", "Animated", "Platform", "StyleSheet", "useWindowDimensions", "View", "AnimatedText", "getConstants", "InputLabel", "props", "labeled", "wiggle", "error", "focused", "opacity", "labelLayoutWidth", "labelLayoutHeight", "labelBackground", "label", "labelError", "onLayoutAnimatedText", "onLabelTextLayout", "hasActiveOutline", "activeColor", "placeholder<PERSON><PERSON><PERSON>", "baseLabelTranslateX", "baseLabelTranslateY", "font", "fontSize", "lineHeight", "fontWeight", "placeholderOpacity", "wiggleOffsetX", "labelScale", "topPosition", "paddingLeft", "paddingRight", "backgroundColor", "roundness", "placeholderColor", "errorColor", "labelTranslationXOffset", "maxFontSizeMultiplier", "testID", "isV3", "inputContainerLayout", "scaledLabel", "INPUT_PADDING_HORIZONTAL", "width", "isWeb", "OS", "paddingOffset", "labelTranslationX", "transform", "translateX", "interpolate", "inputRange", "outputRange", "labelStyle", "translateY", "scale", "labelWidth", "commonStyles", "top", "max<PERSON><PERSON><PERSON>", "textColor", "createElement", "pointerEvents", "style", "absoluteFill", "styles", "overflow", "labelContainer", "variant", "onLayout", "onTextLayout", "color", "numberOfLines", "create", "zIndex", "memo"], "sourceRoot": "../../../../../src", "sources": ["components/TextInput/Label/InputLabel.tsx"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SACEC,QAAQ,EAERC,QAAQ,EACRC,UAAU,EACVC,mBAAmB,EACnBC,IAAI,QACC,cAAc;AAErB,OAAOC,YAAY,MAAM,+BAA+B;AACxD,SAASC,YAAY,QAAQ,YAAY;AAGzC,MAAMC,UAAU,GAAIC,KAAsB,IAAK;EAC7C,MAAM;IACJC,OAAO;IACPC,MAAM;IACNC,KAAK;IACLC,OAAO;IACPC,OAAO;IACPC,gBAAgB;IAChBC,iBAAiB;IACjBC,eAAe;IACfC,KAAK;IACLC,UAAU;IACVC,oBAAoB;IACpBC,iBAAiB;IACjBC,gBAAgB;IAChBC,WAAW;IACXC,gBAAgB;IAChBC,mBAAmB;IACnBC,mBAAmB;IACnBC,IAAI;IACJC,QAAQ;IACRC,UAAU;IACVC,UAAU;IACVC,kBAAkB;IAClBC,aAAa;IACbC,UAAU;IACVC,WAAW;IACXC,WAAW;IACXC,YAAY;IACZC,eAAe;IACfC,SAAS;IACTC,gBAAgB;IAChBC,UAAU;IACVC,uBAAuB;IACvBC,qBAAqB;IACrBC,MAAM;IACNC,IAAI;IACJC,oBAAoB;IACpBC;EACF,CAAC,GAAGrC,KAAK;EAET,MAAM;IAAEsC;EAAyB,CAAC,GAAGxC,YAAY,CAACqC,IAAI,CAAC;EACvD,MAAM;IAAEI;EAAM,CAAC,GAAG5C,mBAAmB,CAAC,CAAC;EAEvC,MAAM6C,KAAK,GAAG/C,QAAQ,CAACgD,EAAE,KAAK,KAAK;EAEnC,MAAMC,aAAa,GACjBhB,WAAW,IAAIC,YAAY,GAAG;IAAED,WAAW;IAAEC;EAAa,CAAC,GAAG,CAAC,CAAC;EAElE,MAAMgB,iBAAiB,GAAG;IACxBC,SAAS,EAAE,CACT;MACE;MACAC,UAAU,EAAE5C,OAAO,CAAC6C,WAAW,CAAC;QAC9BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAClBC,WAAW,EAAE,CAAChC,mBAAmB,EAAEgB,uBAAuB,IAAI,CAAC;MACjE,CAAC;IACH,CAAC;EAEL,CAAC;EAED,MAAMiB,UAAU,GAAG;IACjB,GAAG/B,IAAI;IACPC,QAAQ;IACRC,UAAU;IACVC,UAAU;IACVhB,OAAO,EAAEJ,OAAO,CAAC6C,WAAW,CAAC;MAC3BC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;MAClBC,WAAW,EAAE,CAACnC,gBAAgB,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;IAC3C,CAAC,CAAC;IACF+B,SAAS,EAAE,CACT;MACE;MACAC,UAAU,EAAE3C,MAAM,GACdC,KAAK,CAAC2C,WAAW,CAAC;QAChBC,UAAU,EAAE,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC;QACvBC,WAAW,EAAE,CAAC,CAAC,EAAEzB,aAAa,EAAE,CAAC;MACnC,CAAC,CAAC,GACF;IACN,CAAC,EACD;MACE;MACA2B,UAAU,EACRjC,mBAAmB,KAAK,CAAC,GACrBhB,OAAO,CAAC6C,WAAW,CAAC;QAClBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAClBC,WAAW,EAAE,CAAC/B,mBAAmB,EAAE,CAAC;MACtC,CAAC,CAAC,GACF;IACR,CAAC,EACD;MACE;MACAkC,KAAK,EACH3B,UAAU,KAAK,CAAC,GACZvB,OAAO,CAAC6C,WAAW,CAAC;QAClBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;QAClBC,WAAW,EAAE,CAACxB,UAAU,EAAE,CAAC;MAC7B,CAAC,CAAC,GACFvB;IACR,CAAC;EAEL,CAAC;EAED,MAAMmD,UAAU,GACd,CAAChB,oBAAoB,CAACG,KAAK,GAAGD,wBAAwB,GAAG,CAAC,KACzDD,WAAW,GAAGb,UAAU,GAAG,CAAC,CAAC;EAEhC,MAAM6B,YAAY,GAAG,CACnBtC,gBAAgB,EAChB;IACEuC,GAAG,EAAE7B;EACP,CAAC,EACD;IACE8B,QAAQ,EAAEH;EACZ,CAAC,EACDH,UAAU,EACVP,aAAa,IAAI,CAAC,CAAC,CACpB;EAED,MAAMc,SAAS,GACb9C,UAAU,IAAIqB,UAAU,GAAGA,UAAU,GAAGD,gBAC3B;EAEf;IAAA;IACE;IACA;IACAvC,KAAA,CAAAkE,aAAA,CAAC7D,IAAI;MACH8D,aAAa,EAAC,MAAM;MACpBC,KAAK,EAAE,CAACjE,UAAU,CAACkE,YAAY,EAAEC,MAAM,CAACC,QAAQ,EAAED,MAAM,CAACE,cAAc;IAAE,gBAEzExE,KAAA,CAAAkE,aAAA,CAACjE,QAAQ,CAACI,IAAI;MACZ8D,aAAa,EAAC,MAAM;MACpBC,KAAK,EAAE,CACLjE,UAAU,CAACkE,YAAY,EACvB,CAACpB,KAAK,IAAI;QAAED;MAAM,CAAC,EACnB;QAAElC;MAAQ,CAAC,EACXsC,iBAAiB;IACjB,gBAEFpD,KAAA,CAAAkE,aAAA,CAAC7D,IAAI;MACH+D,KAAK,EAAE;QACLpB,KAAK,EAAEa;MACT;IAAE,GAED5C,eAAe,aAAfA,eAAe,uBAAfA,eAAe,CAAG;MACjBP,OAAO;MACPK,gBAAgB;MAChBC,iBAAiB;MACjB0C,UAAU;MACVlC,gBAAgB;MAChBC,mBAAmB;MACnBS,WAAW;MACXhB,KAAK;MACLmB,eAAe;MACfC,SAAS;MACTI,qBAAqB,EAAEA,qBAAqB;MAC5CC;IACF,CAAC,CAAC,eACF3C,KAAA,CAAAkE,aAAA,CAAC5D,YAAY;MACXmE,OAAO,EAAC,WAAW;MACnBC,QAAQ,EAAEtD,oBAAqB;MAC/BuD,YAAY,EAAEtD,iBAAkB;MAChC+C,KAAK,EAAE,CACLN,YAAY,EACZ;QACEc,KAAK,EAAErD;MACT,CAAC,CACD;MACFsD,aAAa,EAAE,CAAE;MACjBnC,qBAAqB,EAAEA,qBAAsB;MAC7CC,MAAM,EAAE,GAAGA,MAAM;IAAgB,GAEhCzB,KACW,CAAC,eACflB,KAAA,CAAAkE,aAAA,CAAC5D,YAAY;MACXmE,OAAO,EAAE5D,OAAO,GAAG,WAAW,GAAG,WAAY;MAC7CuD,KAAK,EAAE,CACLN,YAAY,EACZ;QACEc,KAAK,EAAEX,SAAS;QAChBnD,OAAO,EAAEiB;MACX,CAAC,CACD;MACF8C,aAAa,EAAE,CAAE;MACjBnC,qBAAqB,EAAEA,qBAAsB;MAC7CC,MAAM,EAAE,GAAGA,MAAM;IAAkB,GAElCzB,KACW,CACV,CACO,CACX;EAAC;AAEX,CAAC;AAED,MAAMoD,MAAM,GAAGnE,UAAU,CAAC2E,MAAM,CAAC;EAC/BP,QAAQ,EAAE;IACRA,QAAQ,EAAE;EACZ,CAAC;EACDC,cAAc,EAAE;IACdO,MAAM,EAAE;EACV;AACF,CAAC,CAAC;AAEF,4BAAe/E,KAAK,CAACgF,IAAI,CAACxE,UAAU,CAAC", "ignoreList": []}