{"version": 3, "names": ["React", "Animated", "TextInput", "NativeTextInput", "TextInputAffix", "TextInputIcon", "TextInputFlat", "TextInputOutlined", "useInternalTheme", "forwardRef", "roundLayoutSize", "BLUR_ANIMATION_DURATION", "FOCUS_ANIMATION_DURATION", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "props", "createElement", "mode", "dense", "disabled", "error", "errorProp", "multiline", "editable", "contentStyle", "render", "theme", "themeOverrides", "rest", "ref", "isControlled", "value", "undefined", "validInputValue", "defaultValue", "current", "labeled", "useRef", "Value", "focused", "setFocused", "useState", "displayPlaceholder", "setDisplayPlaceholder", "uncontrolledValue", "setUncontrolledValue", "labelTextLayout", "setLabelTextLayout", "width", "inputContainerLayout", "setInputContainerLayout", "labelLayout", "setLabelLayout", "measured", "height", "leftLayout", "setLeftLayout", "rightLayout", "setRightLayout", "timer", "root", "scale", "animation", "useImperativeHandle", "focus", "_root$current", "clear", "_root$current2", "setNativeProps", "args", "_root$current3", "isFocused", "_root$current4", "blur", "_root$current5", "forceFocus", "_root$current6", "setSelection", "start", "end", "_root$current7", "useEffect", "timing", "toValue", "duration", "useNativeDriver", "label", "placeholder", "setTimeout", "clearTimeout", "stopAnimation", "onLeftAffixLayoutChange", "useCallback", "event", "nativeEvent", "layout", "onRightAffixLayoutChange", "handleFocus", "_rest$onFocus", "onFocus", "call", "handleBlur", "_rest$onBlur", "onBlur", "handleChangeText", "_rest$onChangeText", "onChangeText", "handleLayoutAnimatedText", "e", "handleLabelTextLayout", "lines", "reduce", "acc", "line", "Math", "ceil", "handleInputContainerLayout", "_root$current8", "maxFontSizeMultiplier", "scaledLabel", "_extends", "parentState", "innerRef", "onLayoutAnimatedText", "onInputLayout", "onLabelTextLayout", "Icon", "Affix"], "sourceRoot": "../../../../src", "sources": ["components/TextInput/TextInput.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SACEC,QAAQ,EAGRC,SAAS,IAAIC,eAAe,QAKvB,cAAc;AAErB,OAAOC,cAAc,MAEd,4BAA4B;AACnC,OAAOC,aAAa,MAEb,2BAA2B;AAClC,OAAOC,aAAa,MAAM,iBAAiB;AAC3C,OAAOC,iBAAiB,MAAM,qBAAqB;AAEnD,SAASC,gBAAgB,QAAQ,oBAAoB;AAErD,SAASC,UAAU,QAAQ,wBAAwB;AACnD,SAASC,eAAe,QAAQ,6BAA6B;AAE7D,MAAMC,uBAAuB,GAAG,GAAG;AACnC,MAAMC,wBAAwB,GAAG,GAAG;AAwKpC,MAAMC,eAAe,GAAIC,KAAkB,iBAAKd,KAAA,CAAAe,aAAA,CAACZ,eAAe,EAAKW,KAAQ,CAAC;;AAE9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMZ,SAAS,GAAGO,UAAU,CAC1B,CACE;EACEO,IAAI,GAAG,MAAM;EACbC,KAAK,GAAG,KAAK;EACbC,QAAQ,GAAG,KAAK;EAChBC,KAAK,EAAEC,SAAS,GAAG,KAAK;EACxBC,SAAS,GAAG,KAAK;EACjBC,QAAQ,GAAG,IAAI;EACfC,YAAY;EACZC,MAAM,GAAGX,eAAe;EACxBY,KAAK,EAAEC,cAAc;EACrB,GAAGC;AACE,CAAC,EACRC,GAAG,KACA;EACH,MAAMH,KAAK,GAAGjB,gBAAgB,CAACkB,cAAc,CAAC;EAC9C,MAAMG,YAAY,GAAGF,IAAI,CAACG,KAAK,KAAKC,SAAS;EAC7C,MAAMC,eAAe,GAAGH,YAAY,GAAGF,IAAI,CAACG,KAAK,GAAGH,IAAI,CAACM,YAAY;EAErE,MAAM;IAAEC,OAAO,EAAEC;EAAQ,CAAC,GAAGnC,KAAK,CAACoC,MAAM,CACvC,IAAInC,QAAQ,CAACoC,KAAK,CAACL,eAAe,GAAG,CAAC,GAAG,CAAC,CAC5C,CAAC;EACD,MAAM;IAAEE,OAAO,EAAEf;EAAM,CAAC,GAAGnB,KAAK,CAACoC,MAAM,CACrC,IAAInC,QAAQ,CAACoC,KAAK,CAACjB,SAAS,GAAG,CAAC,GAAG,CAAC,CACtC,CAAC;EACD,MAAM,CAACkB,OAAO,EAAEC,UAAU,CAAC,GAAGvC,KAAK,CAACwC,QAAQ,CAAU,KAAK,CAAC;EAC5D,MAAM,CAACC,kBAAkB,EAAEC,qBAAqB,CAAC,GAC/C1C,KAAK,CAACwC,QAAQ,CAAU,KAAK,CAAC;EAChC,MAAM,CAACG,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG5C,KAAK,CAACwC,QAAQ,CAE9DR,eAAe,CAAC;EAClB;EACA,MAAMF,KAAK,GAAGD,YAAY,GAAGF,IAAI,CAACG,KAAK,GAAGa,iBAAiB;EAE3D,MAAM,CAACE,eAAe,EAAEC,kBAAkB,CAAC,GAAG9C,KAAK,CAACwC,QAAQ,CAAC;IAC3DO,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAM,CAACC,oBAAoB,EAAEC,uBAAuB,CAAC,GAAGjD,KAAK,CAACwC,QAAQ,CAAC;IACrEO,KAAK,EAAE;EACT,CAAC,CAAC;EAEF,MAAM,CAACG,WAAW,EAAEC,cAAc,CAAC,GAAGnD,KAAK,CAACwC,QAAQ,CAIjD;IACDY,QAAQ,EAAE,KAAK;IACfL,KAAK,EAAE,CAAC;IACRM,MAAM,EAAE;EACV,CAAC,CAAC;EACF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGvD,KAAK,CAACwC,QAAQ,CAG/C;IACDO,KAAK,EAAE,IAAI;IACXM,MAAM,EAAE;EACV,CAAC,CAAC;EACF,MAAM,CAACG,WAAW,EAAEC,cAAc,CAAC,GAAGzD,KAAK,CAACwC,QAAQ,CAGjD;IACDO,KAAK,EAAE,IAAI;IACXM,MAAM,EAAE;EACV,CAAC,CAAC;EAEF,MAAMK,KAAK,GAAG1D,KAAK,CAACoC,MAAM,CAA6BL,SAAS,CAAC;EACjE,MAAM4B,IAAI,GAAG3D,KAAK,CAACoC,MAAM,CAAqC,IAAI,CAAC;EAEnE,MAAM;IAAEwB;EAAM,CAAC,GAAGnC,KAAK,CAACoC,SAAS;EAEjC7D,KAAK,CAAC8D,mBAAmB,CAAClC,GAAG,EAAE,OAAO;IACpCmC,KAAK,EAAEA,CAAA;MAAA,IAAAC,aAAA;MAAA,QAAAA,aAAA,GAAML,IAAI,CAACzB,OAAO,cAAA8B,aAAA,uBAAZA,aAAA,CAAcD,KAAK,CAAC,CAAC;IAAA;IAClCE,KAAK,EAAEA,CAAA;MAAA,IAAAC,cAAA;MAAA,QAAAA,cAAA,GAAMP,IAAI,CAACzB,OAAO,cAAAgC,cAAA,uBAAZA,cAAA,CAAcD,KAAK,CAAC,CAAC;IAAA;IAClCE,cAAc,EAAGC,IAAY;MAAA,IAAAC,cAAA;MAAA,QAAAA,cAAA,GAAKV,IAAI,CAACzB,OAAO,cAAAmC,cAAA,uBAAZA,cAAA,CAAcF,cAAc,CAACC,IAAI,CAAC;IAAA;IACpEE,SAAS,EAAEA,CAAA;MAAA,IAAAC,cAAA;MAAA,OAAM,EAAAA,cAAA,GAAAZ,IAAI,CAACzB,OAAO,cAAAqC,cAAA,uBAAZA,cAAA,CAAcD,SAAS,CAAC,CAAC,KAAI,KAAK;IAAA;IACnDE,IAAI,EAAEA,CAAA;MAAA,IAAAC,cAAA;MAAA,QAAAA,cAAA,GAAMd,IAAI,CAACzB,OAAO,cAAAuC,cAAA,uBAAZA,cAAA,CAAcD,IAAI,CAAC,CAAC;IAAA;IAChCE,UAAU,EAAEA,CAAA;MAAA,IAAAC,cAAA;MAAA,QAAAA,cAAA,GAAMhB,IAAI,CAACzB,OAAO,cAAAyC,cAAA,uBAAZA,cAAA,CAAcZ,KAAK,CAAC,CAAC;IAAA;IACvCa,YAAY,EAAEA,CAACC,KAAa,EAAEC,GAAW;MAAA,IAAAC,cAAA;MAAA,QAAAA,cAAA,GACvCpB,IAAI,CAACzB,OAAO,cAAA6C,cAAA,uBAAZA,cAAA,CAAcH,YAAY,CAACC,KAAK,EAAEC,GAAG,CAAC;IAAA;EAC1C,CAAC,CAAC,CAAC;EAEH9E,KAAK,CAACgF,SAAS,CAAC,MAAM;IACpB;IACA,IAAI5D,SAAS,EAAE;MACb;MACAnB,QAAQ,CAACgF,MAAM,CAAC9D,KAAK,EAAE;QACrB+D,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAEvE,wBAAwB,GAAGgD,KAAK;QAC1C;QACAwB,eAAe,EAAE;MACnB,CAAC,CAAC,CAACP,KAAK,CAAC,CAAC;IACZ,CAAC,MAAM;MACL;MACA;QACE5E,QAAQ,CAACgF,MAAM,CAAC9D,KAAK,EAAE;UACrB+D,OAAO,EAAE,CAAC;UACVC,QAAQ,EAAExE,uBAAuB,GAAGiD,KAAK;UACzC;UACAwB,eAAe,EAAE;QACnB,CAAC,CAAC,CAACP,KAAK,CAAC,CAAC;MACZ;IACF;EACF,CAAC,EAAE,CAACzD,SAAS,EAAEwC,KAAK,EAAEzC,KAAK,CAAC,CAAC;EAE7BnB,KAAK,CAACgF,SAAS,CAAC,MAAM;IACpB;IACA;IACA;IACA,IAAI1C,OAAO,IAAI,CAACX,IAAI,CAAC0D,KAAK,EAAE;MAC1B;MACA;MACA,IAAI1D,IAAI,CAAC2D,WAAW,EAAE;QACpB;QACA;QACA5B,KAAK,CAACxB,OAAO,GAAGqD,UAAU,CACxB,MAAM7C,qBAAqB,CAAC,IAAI,CAAC,EACjC,EACF,CAA8B;MAChC;IACF,CAAC,MAAM;MACL;MACAA,qBAAqB,CAAC,KAAK,CAAC;IAC9B;IAEA,OAAO,MAAM;MACX,IAAIgB,KAAK,CAACxB,OAAO,EAAE;QACjBsD,YAAY,CAAC9B,KAAK,CAACxB,OAAO,CAAC;MAC7B;IACF,CAAC;EACH,CAAC,EAAE,CAACI,OAAO,EAAEX,IAAI,CAAC0D,KAAK,EAAE1D,IAAI,CAAC2D,WAAW,CAAC,CAAC;EAE3CtF,KAAK,CAACgF,SAAS,CAAC,MAAM;IACpB7C,OAAO,CAACsD,aAAa,CAAC,CAAC;IACvB;IACA;IACA;IACA;IACA,IAAI3D,KAAK,IAAIQ,OAAO,EAAE;MACpB;MACArC,QAAQ,CAACgF,MAAM,CAAC9C,OAAO,EAAE;QACvB+C,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAExE,uBAAuB,GAAGiD,KAAK;QACzC;QACAwB,eAAe,EAAE;MACnB,CAAC,CAAC,CAACP,KAAK,CAAC,CAAC;IACZ,CAAC,MAAM;MACL;MACA5E,QAAQ,CAACgF,MAAM,CAAC9C,OAAO,EAAE;QACvB+C,OAAO,EAAE,CAAC;QACVC,QAAQ,EAAEvE,wBAAwB,GAAGgD,KAAK;QAC1C;QACAwB,eAAe,EAAE;MACnB,CAAC,CAAC,CAACP,KAAK,CAAC,CAAC;IACZ;EACF,CAAC,EAAE,CAACvC,OAAO,EAAER,KAAK,EAAEK,OAAO,EAAEyB,KAAK,CAAC,CAAC;EAEpC,MAAM8B,uBAAuB,GAAG1F,KAAK,CAAC2F,WAAW,CAC9CC,KAAwB,IAAK;IAC5B,MAAMvC,MAAM,GAAG3C,eAAe,CAACkF,KAAK,CAACC,WAAW,CAACC,MAAM,CAACzC,MAAM,CAAC;IAC/D,MAAMN,KAAK,GAAGrC,eAAe,CAACkF,KAAK,CAACC,WAAW,CAACC,MAAM,CAAC/C,KAAK,CAAC;IAE7D,IAAIA,KAAK,KAAKO,UAAU,CAACP,KAAK,IAAIM,MAAM,KAAKC,UAAU,CAACD,MAAM,EAAE;MAC9DE,aAAa,CAAC;QACZR,KAAK;QACLM;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EACD,CAACC,UAAU,CAACD,MAAM,EAAEC,UAAU,CAACP,KAAK,CACtC,CAAC;EAED,MAAMgD,wBAAwB,GAAG/F,KAAK,CAAC2F,WAAW,CAC/CC,KAAwB,IAAK;IAC5B,MAAM7C,KAAK,GAAGrC,eAAe,CAACkF,KAAK,CAACC,WAAW,CAACC,MAAM,CAAC/C,KAAK,CAAC;IAC7D,MAAMM,MAAM,GAAG3C,eAAe,CAACkF,KAAK,CAACC,WAAW,CAACC,MAAM,CAACzC,MAAM,CAAC;IAE/D,IAAIN,KAAK,KAAKS,WAAW,CAACT,KAAK,IAAIM,MAAM,KAAKG,WAAW,CAACH,MAAM,EAAE;MAChEI,cAAc,CAAC;QACbV,KAAK;QACLM;MACF,CAAC,CAAC;IACJ;EACF,CAAC,EACD,CAACG,WAAW,CAACH,MAAM,EAAEG,WAAW,CAACT,KAAK,CACxC,CAAC;EAED,MAAMiD,WAAW,GAAI5B,IAAS,IAAK;IAAA,IAAA6B,aAAA;IACjC,IAAI/E,QAAQ,IAAI,CAACI,QAAQ,EAAE;MACzB;IACF;IAEAiB,UAAU,CAAC,IAAI,CAAC;IAEhB,CAAA0D,aAAA,GAAAtE,IAAI,CAACuE,OAAO,cAAAD,aAAA,eAAZA,aAAA,CAAAE,IAAA,CAAAxE,IAAI,EAAWyC,IAAI,CAAC;EACtB,CAAC;EAED,MAAMgC,UAAU,GAAIhC,IAAY,IAAK;IAAA,IAAAiC,YAAA;IACnC,IAAI,CAAC/E,QAAQ,EAAE;MACb;IACF;IAEAiB,UAAU,CAAC,KAAK,CAAC;IACjB,CAAA8D,YAAA,GAAA1E,IAAI,CAAC2E,MAAM,cAAAD,YAAA,eAAXA,YAAA,CAAAF,IAAA,CAAAxE,IAAI,EAAUyC,IAAI,CAAC;EACrB,CAAC;EAED,MAAMmC,gBAAgB,GAAIzE,KAAa,IAAK;IAAA,IAAA0E,kBAAA;IAC1C,IAAI,CAAClF,QAAQ,IAAIJ,QAAQ,EAAE;MACzB;IACF;IAEA,IAAI,CAACW,YAAY,EAAE;MACjB;MACAe,oBAAoB,CAACd,KAAK,CAAC;IAC7B;IACA,CAAA0E,kBAAA,GAAA7E,IAAI,CAAC8E,YAAY,cAAAD,kBAAA,eAAjBA,kBAAA,CAAAL,IAAA,CAAAxE,IAAI,EAAgBG,KAAK,CAAC;EAC5B,CAAC;EAED,MAAM4E,wBAAwB,GAAG1G,KAAK,CAAC2F,WAAW,CAC/CgB,CAAoB,IAAK;IACxB,MAAM5D,KAAK,GAAGrC,eAAe,CAACiG,CAAC,CAACd,WAAW,CAACC,MAAM,CAAC/C,KAAK,CAAC;IACzD,MAAMM,MAAM,GAAG3C,eAAe,CAACiG,CAAC,CAACd,WAAW,CAACC,MAAM,CAACzC,MAAM,CAAC;IAE3D,IAAIN,KAAK,KAAKG,WAAW,CAACH,KAAK,IAAIM,MAAM,KAAKH,WAAW,CAACG,MAAM,EAAE;MAChEF,cAAc,CAAC;QACbJ,KAAK;QACLM,MAAM;QACND,QAAQ,EAAE;MACZ,CAAC,CAAC;IACJ;EACF,CAAC,EACD,CAACF,WAAW,CAACG,MAAM,EAAEH,WAAW,CAACH,KAAK,CACxC,CAAC;EAED,MAAM6D,qBAAqB,GAAG5G,KAAK,CAAC2F,WAAW,CAC7C,CAAC;IAAEE;EAAuD,CAAC,KAAK;IAC9D/C,kBAAkB,CAAC;MACjBC,KAAK,EAAE8C,WAAW,CAACgB,KAAK,CAACC,MAAM,CAC7B,CAACC,GAAG,EAAEC,IAAI,KAAKD,GAAG,GAAGE,IAAI,CAACC,IAAI,CAACF,IAAI,CAACjE,KAAK,CAAC,EAC1C,CACF;IACF,CAAC,CAAC;EACJ,CAAC,EACD,EACF,CAAC;EAED,MAAMoE,0BAA0B,GAAGnH,KAAK,CAAC2F,WAAW,CAClD,CAAC;IAAEE,WAAW,EAAE;MAAEC;IAAO;EAAqB,CAAC,KAAK;IAClD7C,uBAAuB,CAAC;MACtBF,KAAK,EAAE+C,MAAM,CAAC/C;IAChB,CAAC,CAAC;EACJ,CAAC,EACD,EACF,CAAC;EAED,MAAM2B,UAAU,GAAG1E,KAAK,CAAC2F,WAAW,CAAC;IAAA,IAAAyB,cAAA;IAAA,QAAAA,cAAA,GAAMzD,IAAI,CAACzB,OAAO,cAAAkF,cAAA,uBAAZA,cAAA,CAAcrD,KAAK,CAAC,CAAC;EAAA,GAAE,EAAE,CAAC;EAErE,MAAM;IAAEsD,qBAAqB,GAAG;EAAI,CAAC,GAAG1F,IAAI;EAE5C,MAAM2F,WAAW,GAAG,CAAC,EAAExF,KAAK,IAAIQ,OAAO,CAAC;EAExC,IAAItB,IAAI,KAAK,UAAU,EAAE;IACvB,oBACEhB,KAAA,CAAAe,aAAA,CAACR,iBAAiB,EAAAgH,QAAA;MAChBtG,KAAK,EAAEA,KAAM;MACbC,QAAQ,EAAEA,QAAS;MACnBC,KAAK,EAAEC,SAAU;MACjBC,SAAS,EAAEA,SAAU;MACrBC,QAAQ,EAAEA,QAAS;MACnBE,MAAM,EAAEA;IAAO,GACXG,IAAI;MACRF,KAAK,EAAEA,KAAM;MACbK,KAAK,EAAEA,KAAM;MACb0F,WAAW,EAAE;QACXrF,OAAO;QACPhB,KAAK;QACLmB,OAAO;QACPG,kBAAkB;QAClBX,KAAK;QACLe,eAAe;QACfK,WAAW;QACXI,UAAU;QACVE,WAAW;QACXR;MACF,CAAE;MACFyE,QAAQ,EAAG7F,GAAG,IAAK;QACjB+B,IAAI,CAACzB,OAAO,GAAGN,GAAG;MACpB,CAAE;MACFsE,OAAO,EAAEF,WAAY;MACrBtB,UAAU,EAAEA,UAAW;MACvB4B,MAAM,EAAEF,UAAW;MACnBK,YAAY,EAAEF,gBAAiB;MAC/BmB,oBAAoB,EAAEhB,wBAAyB;MAC/CiB,aAAa,EAAER,0BAA2B;MAC1CS,iBAAiB,EAAEhB,qBAAsB;MACzClB,uBAAuB,EAAEA,uBAAwB;MACjDK,wBAAwB,EAAEA,wBAAyB;MACnDsB,qBAAqB,EAAEA,qBAAsB;MAC7C9F,YAAY,EAAEA,YAAa;MAC3B+F,WAAW,EAAEA;IAAY,EAC1B,CAAC;EAEN;EAEA,oBACEtH,KAAA,CAAAe,aAAA,CAACT,aAAa,EAAAiH,QAAA;IACZtG,KAAK,EAAEA,KAAM;IACbC,QAAQ,EAAEA,QAAS;IACnBC,KAAK,EAAEC,SAAU;IACjBC,SAAS,EAAEA,SAAU;IACrBC,QAAQ,EAAEA,QAAS;IACnBE,MAAM,EAAEA;EAAO,GACXG,IAAI;IACRF,KAAK,EAAEA,KAAM;IACbK,KAAK,EAAEA,KAAM;IACb0F,WAAW,EAAE;MACXrF,OAAO;MACPhB,KAAK;MACLmB,OAAO;MACPG,kBAAkB;MAClBX,KAAK;MACLe,eAAe;MACfK,WAAW;MACXI,UAAU;MACVE,WAAW;MACXR;IACF,CAAE;IACFyE,QAAQ,EAAG7F,GAAG,IAAK;MACjB+B,IAAI,CAACzB,OAAO,GAAGN,GAAG;IACpB,CAAE;IACFsE,OAAO,EAAEF,WAAY;IACrBtB,UAAU,EAAEA,UAAW;IACvB4B,MAAM,EAAEF,UAAW;IACnBuB,aAAa,EAAER,0BAA2B;IAC1CV,YAAY,EAAEF,gBAAiB;IAC/BmB,oBAAoB,EAAEhB,wBAAyB;IAC/CkB,iBAAiB,EAAEhB,qBAAsB;IACzClB,uBAAuB,EAAEA,uBAAwB;IACjDK,wBAAwB,EAAEA,wBAAyB;IACnDsB,qBAAqB,EAAEA,qBAAsB;IAC7C9F,YAAY,EAAEA,YAAa;IAC3B+F,WAAW,EAAEA;EAAY,EAC1B,CAAC;AAEN,CACF,CAAwB;AACxB;AACApH,SAAS,CAAC2H,IAAI,GAAGxH,aAAa;;AAE9B;AACA;AACAH,SAAS,CAAC4H,KAAK,GAAG1H,cAAc;AAEhC,eAAeF,SAAS", "ignoreList": []}