{"version": 3, "names": ["React", "StyleSheet", "StyledText", "Headline", "props", "createElement", "_extends", "alpha", "family", "style", "styles", "text", "create", "fontSize", "lineHeight", "marginVertical", "letterSpacing"], "sourceRoot": "../../../../../src", "sources": ["components/Typography/v2/Headline.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAA0BC,UAAU,QAAmB,cAAc;AAErE,OAAOC,UAAU,MAAM,cAAc;AAOrC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,QAAQ,GAAIC,KAAY,IAAK;EACjC,oBACEJ,KAAA,CAAAK,aAAA,CAACH,UAAU,EAAAI,QAAA,KACLF,KAAK;IACTG,KAAK,EAAE,IAAK;IACZC,MAAM,EAAC,SAAS;IAChBC,KAAK,EAAE,CAACC,MAAM,CAACC,IAAI,EAAEP,KAAK,CAACK,KAAK;EAAE,EACnC,CAAC;AAEN,CAAC;AAED,eAAeN,QAAQ;AAEvB,MAAMO,MAAM,GAAGT,UAAU,CAACW,MAAM,CAAC;EAC/BD,IAAI,EAAE;IACJE,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,EAAE;IACdC,cAAc,EAAE,CAAC;IACjBC,aAAa,EAAE;EACjB;AACF,CAAC,CAAC", "ignoreList": []}