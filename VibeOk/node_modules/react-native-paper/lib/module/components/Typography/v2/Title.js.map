{"version": 3, "names": ["React", "StyleSheet", "StyledText", "Title", "props", "createElement", "_extends", "alpha", "family", "style", "styles", "text", "create", "fontSize", "lineHeight", "marginVertical", "letterSpacing"], "sourceRoot": "../../../../../src", "sources": ["components/Typography/v2/Title.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAAeC,UAAU,QAAQ,cAAc;AAE/C,OAAOC,UAAU,MAAM,cAAc;AAMrC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,KAAK,GAAIC,KAAY,iBACzBJ,KAAA,CAAAK,aAAA,CAACH,UAAU,EAAAI,QAAA,KACLF,KAAK;EACTG,KAAK,EAAE,IAAK;EACZC,MAAM,EAAC,QAAQ;EACfC,KAAK,EAAE,CAACC,MAAM,CAACC,IAAI,EAAEP,KAAK,CAACK,KAAK;AAAE,EACnC,CACF;AAED,eAAeN,KAAK;AAEpB,MAAMO,MAAM,GAAGT,UAAU,CAACW,MAAM,CAAC;EAC/BD,IAAI,EAAE;IACJE,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,EAAE;IACdC,cAAc,EAAE,CAAC;IACjBC,aAAa,EAAE;EACjB;AACF,CAAC,CAAC", "ignoreList": []}