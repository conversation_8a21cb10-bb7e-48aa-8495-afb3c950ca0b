{"version": 3, "names": ["React", "StyleSheet", "StyledText", "Subheading", "props", "createElement", "_extends", "alpha", "family", "style", "styles", "text", "create", "fontSize", "lineHeight", "marginVertical", "letterSpacing"], "sourceRoot": "../../../../../src", "sources": ["components/Typography/v2/Subheading.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAA0BC,UAAU,QAAmB,cAAc;AAErE,OAAOC,UAAU,MAAM,cAAc;AAOrC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,GAAIC,KAAY,iBAC9BJ,KAAA,CAAAK,aAAA,CAACH,UAAU,EAAAI,QAAA,KACLF,KAAK;EACTG,KAAK,EAAE,IAAK;EACZC,MAAM,EAAC,SAAS;EAChBC,KAAK,EAAE,CAACC,MAAM,CAACC,IAAI,EAAEP,KAAK,CAACK,KAAK;AAAE,EACnC,CACF;AAED,eAAeN,UAAU;AAEzB,MAAMO,MAAM,GAAGT,UAAU,CAACW,MAAM,CAAC;EAC/BD,IAAI,EAAE;IACJE,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,EAAE;IACdC,cAAc,EAAE,CAAC;IACjBC,aAAa,EAAE;EACjB;AACF,CAAC,CAAC", "ignoreList": []}