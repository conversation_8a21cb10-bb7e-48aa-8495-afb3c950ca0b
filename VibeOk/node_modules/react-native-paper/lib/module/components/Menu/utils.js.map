{"version": 3, "names": ["color", "black", "white", "MIN_WIDTH", "MAX_WIDTH", "getDisabledColor", "theme", "isV3", "colors", "onSurfaceDisabled", "dark", "alpha", "rgb", "string", "getTitleColor", "disabled", "onSurface", "text", "getIconColor", "onSurfaceVariant", "getRippleColor", "customRippleColor", "undefined", "getMenuItemColor", "titleColor", "iconColor", "rippleColor", "getContentMaxWidth", "iconWidth", "leadingIcon", "trailingIcon"], "sourceRoot": "../../../../src", "sources": ["components/Menu/utils.ts"], "mappings": "AAEA,OAAOA,KAAK,MAAM,OAAO;AAEzB,SAASC,KAAK,EAAEC,KAAK,QAAQ,+BAA+B;AAI5D,OAAO,MAAMC,SAAS,GAAG,GAAG;AAC5B,OAAO,MAAMC,SAAS,GAAG,GAAG;AAe5B,MAAMC,gBAAgB,GAAIC,KAAoB,IAAK;EACjD,IAAIA,KAAK,CAACC,IAAI,EAAE;IACd,OAAOD,KAAK,CAACE,MAAM,CAACC,iBAAiB;EACvC;EAEA,OAAOT,KAAK,CAACM,KAAK,CAACI,IAAI,GAAGR,KAAK,GAAGD,KAAK,CAAC,CACrCU,KAAK,CAAC,IAAI,CAAC,CACXC,GAAG,CAAC,CAAC,CACLC,MAAM,CAAC,CAAC;AACb,CAAC;AAED,MAAMC,aAAa,GAAGA,CAAC;EAAER,KAAK;EAAES;AAAqB,CAAC,KAAK;EACzD,IAAIA,QAAQ,EAAE;IACZ,OAAOV,gBAAgB,CAACC,KAAK,CAAC;EAChC;EAEA,IAAIA,KAAK,CAACC,IAAI,EAAE;IACd,OAAOD,KAAK,CAACE,MAAM,CAACQ,SAAS;EAC/B;EAEA,OAAOhB,KAAK,CAACM,KAAK,CAACE,MAAM,CAACS,IAAI,CAAC,CAACN,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;AAC5D,CAAC;AAED,MAAMK,YAAY,GAAGA,CAAC;EAAEZ,KAAK;EAAES;AAAqB,CAAC,KAAK;EACxD,IAAIA,QAAQ,EAAE;IACZ,OAAOV,gBAAgB,CAACC,KAAK,CAAC;EAChC;EAEA,IAAIA,KAAK,CAACC,IAAI,EAAE;IACd,OAAOD,KAAK,CAACE,MAAM,CAACW,gBAAgB;EACtC;EAEA,OAAOnB,KAAK,CAACM,KAAK,CAACE,MAAM,CAACS,IAAI,CAAC,CAACN,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;AAC5D,CAAC;AAED,MAAMO,cAAc,GAAGA,CAAC;EACtBd,KAAK;EACLe;AAC4B,CAAC,KAAK;EAClC,IAAIA,iBAAiB,EAAE;IACrB,OAAOA,iBAAiB;EAC1B;EAEA,IAAIf,KAAK,CAACC,IAAI,EAAE;IACd,OAAOP,KAAK,CAACM,KAAK,CAACE,MAAM,CAACW,gBAAgB,CAAC,CAACR,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EACxE;EAEA,OAAOS,SAAS;AAClB,CAAC;AAED,OAAO,MAAMC,gBAAgB,GAAGA,CAAC;EAC/BjB,KAAK;EACLS,QAAQ;EACRM;AACU,CAAC,KAAK;EAChB,OAAO;IACLG,UAAU,EAAEV,aAAa,CAAC;MAAER,KAAK;MAAES;IAAS,CAAC,CAAC;IAC9CU,SAAS,EAAEP,YAAY,CAAC;MAAEZ,KAAK;MAAES;IAAS,CAAC,CAAC;IAC5CW,WAAW,EAAEN,cAAc,CAAC;MAAEd,KAAK;MAAEe;IAAkB,CAAC;EAC1D,CAAC;AACH,CAAC;AAED,OAAO,MAAMM,kBAAkB,GAAGA,CAAC;EACjCpB,IAAI;EACJqB,SAAS;EACTC,WAAW;EACXC;AACY,CAAC,KAAK;EAClB,IAAIvB,IAAI,EAAE;IACR,IAAIsB,WAAW,IAAIC,YAAY,EAAE;MAC/B,OAAO1B,SAAS,IAAI,CAAC,GAAGwB,SAAS,GAAG,EAAE,CAAC;IACzC;IAEA,IAAIC,WAAW,IAAIC,YAAY,EAAE;MAC/B,OAAO1B,SAAS,IAAIwB,SAAS,GAAG,EAAE,CAAC;IACrC;IAEA,OAAOxB,SAAS,GAAG,EAAE;EACvB;EAEA,IAAIyB,WAAW,EAAE;IACf,OAAOzB,SAAS,IAAIwB,SAAS,GAAG,EAAE,CAAC;EACrC;EAEA,OAAOxB,SAAS,GAAG,EAAE;AACvB,CAAC", "ignoreList": []}