# VibeOk - Video Conferencing Mobile App

VibeOk is a cross-platform mobile video conferencing application built with React Native, Expo, ZEGOCLOUD UIKits, and Supabase.

## Features

- 🔐 **Secure Authentication** - Email/password authentication with Supabase Auth
- 📱 **Cross-Platform** - Works on both iOS and Android via Expo
- 🎥 **Video Conferencing** - High-quality video calls powered by ZEGOCLOUD
- 🏠 **Meeting Rooms** - Create and join meetings with unique room codes
- 👥 **Multi-Participant** - Support for up to 10 participants per meeting
- 📊 **Meeting History** - Track your past and active meetings
- 👤 **User Profiles** - Manage your profile and account settings
- 🔒 **Privacy & Security** - Row Level Security (RLS) with Supabase

## Tech Stack

- **Frontend**: React Native with Expo
- **Navigation**: React Navigation
- **Video/Audio**: ZEGOCLOUD UIKits
- **Backend**: Supabase (Database, Auth, Real-time)
- **State Management**: React Context
- **UI Components**: React Native Paper, Expo Vector Icons
- **Language**: TypeScript

## Prerequisites

Before you begin, ensure you have the following installed:

- [Node.js](https://nodejs.org/) (v16 or later)
- [Expo CLI](https://docs.expo.dev/get-started/installation/)
- [Expo Go](https://expo.dev/client) app on your mobile device
- A [Supabase](https://supabase.com/) account
- A [ZEGOCLOUD](https://www.zegocloud.com/) account

## Setup Instructions

### 1. Clone and Install Dependencies

```bash
cd VibeOk
npm install
```

### 2. Supabase Setup

1. Create a new project in [Supabase](https://supabase.com/)
2. Go to your project's SQL Editor
3. Run the SQL script from `supabase-schema.sql` to create the database schema
4. Go to Settings > API to get your project URL and anon key
5. Update `src/constants/config.ts` with your Supabase credentials:

```typescript
export const SUPABASE_CONFIG = {
  url: 'YOUR_SUPABASE_PROJECT_URL',
  anonKey: 'YOUR_SUPABASE_ANON_KEY',
};
```

### 3. ZEGOCLOUD Setup

1. Create an account at [ZEGOCLOUD](https://www.zegocloud.com/)
2. Create a new project in the ZEGOCLOUD console
3. Get your App ID and App Sign from the project settings
4. Update `src/constants/config.ts` with your ZEGOCLOUD credentials:

```typescript
export const ZEGO_CONFIG = {
  appID: YOUR_ZEGO_APP_ID, // Replace with your actual App ID (number)
  appSign: 'YOUR_ZEGO_APP_SIGN', // Replace with your actual App Sign
};
```

### 4. Run the Application

```bash
# Start the Expo development server
npm start

# Or run on specific platforms
npm run android  # For Android
npm run ios      # For iOS
npm run web      # For web (limited functionality)
```

### 5. Test on Device

1. Install the Expo Go app on your mobile device
2. Scan the QR code displayed in your terminal or browser
3. The app will load on your device

## Project Structure

```
VibeOk/
├── src/
│   ├── components/          # Reusable UI components
│   ├── contexts/           # React Context providers
│   │   ├── AuthContext.tsx
│   │   └── MeetingContext.tsx
│   ├── navigation/         # Navigation configuration
│   │   └── AppNavigator.tsx
│   ├── screens/           # Screen components
│   │   ├── auth/          # Authentication screens
│   │   ├── main/          # Main app screens
│   │   └── LoadingScreen.tsx
│   ├── services/          # API and external services
│   │   └── supabase.ts
│   ├── types/             # TypeScript type definitions
│   │   └── index.ts
│   ├── utils/             # Utility functions
│   │   └── helpers.ts
│   └── constants/         # App constants and configuration
│       └── config.ts
├── assets/                # Static assets
├── supabase-schema.sql    # Database schema
└── README.md
```

## Key Features Explained

### Authentication Flow
- Users can register with email/password
- Email verification is handled by Supabase
- Session persistence keeps users logged in
- Secure logout functionality

### Meeting Management
- **Create Meeting**: Generates unique 8-character room codes
- **Join Meeting**: Enter room code to join existing meetings
- **Meeting History**: View past and active meetings
- **Real-time Updates**: Meeting status updates in real-time

### Video Conferencing
- **ZEGOCLOUD Integration**: High-quality video/audio streaming
- **Meeting Controls**: Mute/unmute, camera on/off, leave meeting
- **Multi-participant Support**: Up to 10 participants per meeting
- **Mobile Optimized**: Designed specifically for mobile devices

### Data Security
- **Row Level Security**: Users can only access their own data
- **JWT Authentication**: Secure API access with Supabase Auth
- **Real-time Subscriptions**: Live updates without polling

## Configuration Options

### App Configuration (`src/constants/config.ts`)

```typescript
export const APP_CONFIG = {
  name: 'VibeOk',
  version: '1.0.0',
  roomCodeLength: 8,        // Length of meeting room codes
  maxParticipants: 10,      // Maximum participants per meeting
};
```

### Customizing Colors and Styling

Update the `COLORS` object in `src/constants/config.ts` to customize the app's appearance:

```typescript
export const COLORS = {
  primary: '#6366f1',       // Primary brand color
  secondary: '#10b981',     // Secondary accent color
  background: '#f8fafc',    // App background
  surface: '#ffffff',       // Card/surface background
  // ... more colors
};
```

## Troubleshooting

### Common Issues

1. **ZEGOCLOUD not working**: Ensure your App ID and App Sign are correctly configured
2. **Supabase connection issues**: Check your project URL and anon key
3. **Build errors**: Make sure all dependencies are installed with `npm install`
4. **Camera/Microphone permissions**: Grant permissions when prompted on device

### Development Tips

1. **Hot Reload**: The app supports hot reload for faster development
2. **Debugging**: Use React Native Debugger or Expo DevTools
3. **Testing**: Test on both iOS and Android devices
4. **Performance**: Monitor performance with Expo's built-in tools

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly on both platforms
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Check the [Expo Documentation](https://docs.expo.dev/)
- Visit [ZEGOCLOUD Documentation](https://docs.zegocloud.com/)
- Check [Supabase Documentation](https://supabase.com/docs)

## Acknowledgments

- [Expo](https://expo.dev/) for the amazing development platform
- [ZEGOCLOUD](https://www.zegocloud.com/) for video conferencing capabilities
- [Supabase](https://supabase.com/) for backend services
- [React Navigation](https://reactnavigation.org/) for navigation
