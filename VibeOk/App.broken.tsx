import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ActivityIndicator } from 'react-native';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';

import ErrorBoundary from './src/components/ErrorBoundary';
import { COLORS, FONT_SIZES, SPACING } from './src/constants/config';

// Import components directly
import { AuthProvider } from './src/contexts/AuthContext';
import { MeetingProvider } from './src/contexts/MeetingContext';
import AppNavigator from './src/navigation/AppNavigator';

function LoadingScreen() {
  return (
    <View style={styles.loadingContainer}>
      <ActivityIndicator size="large" color={COLORS.primary} />
      <Text style={styles.loadingText}>Loading VibeOk...</Text>
    </View>
  );
}

function ConfigCheck() {
  const [configStatus, setConfigStatus] = useState<{
    isValid: boolean;
    errors: string[];
  }>({ isValid: false, errors: [] });

  useEffect(() => {
    const errors: string[] = [];
    
    // Check environment variables
    if (!process.env.EXPO_PUBLIC_SUPABASE_URL || process.env.EXPO_PUBLIC_SUPABASE_URL.includes('your-project-id')) {
      errors.push('Supabase URL not configured');
    }
    
    if (!process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY.includes('your-supabase-anon-key')) {
      errors.push('Supabase anon key not configured');
    }
    
    if (!process.env.EXPO_PUBLIC_ZEGO_APP_ID || process.env.EXPO_PUBLIC_ZEGO_APP_ID === '0') {
      errors.push('ZEGOCLOUD App ID not configured');
    }
    
    if (!process.env.EXPO_PUBLIC_ZEGO_APP_SIGN || process.env.EXPO_PUBLIC_ZEGO_APP_SIGN.includes('your-zego-app-sign')) {
      errors.push('ZEGOCLOUD App Sign not configured');
    }

    setConfigStatus({
      isValid: errors.length === 0,
      errors,
    });
  }, []);

  if (!configStatus.isValid) {
    return (
      <View style={styles.configErrorContainer}>
        <Text style={styles.configErrorTitle}>⚙️ Configuration Required</Text>
        <Text style={styles.configErrorSubtitle}>
          Please configure your environment variables:
        </Text>
        
        <View style={styles.errorList}>
          {configStatus.errors.map((error, index) => (
            <Text key={index} style={styles.errorItem}>
              • {error}
            </Text>
          ))}
        </View>
        
        <Text style={styles.configHelpText}>
          Check your .env file and make sure all credentials are properly set.
        </Text>
      </View>
    );
  }

  return (
    <ErrorBoundary>
      <AuthProvider>
        <ErrorBoundary>
          <MeetingProvider>
            <ErrorBoundary>
              <AppNavigator />
            </ErrorBoundary>
          </MeetingProvider>
        </ErrorBoundary>
      </AuthProvider>
    </ErrorBoundary>
  );
}

export default function App() {
  return (
    <ErrorBoundary>
      <SafeAreaProvider>
        <StatusBar style="auto" />
        <ConfigCheck />
      </SafeAreaProvider>
    </ErrorBoundary>
  );
}

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    backgroundColor: COLORS.background,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.text,
    marginTop: SPACING.md,
  },
  configErrorContainer: {
    flex: 1,
    backgroundColor: COLORS.background,
    padding: SPACING.lg,
    justifyContent: 'center',
  },
  configErrorTitle: {
    fontSize: FONT_SIZES.xxl,
    fontWeight: 'bold',
    color: COLORS.warning,
    textAlign: 'center',
    marginBottom: SPACING.md,
  },
  configErrorSubtitle: {
    fontSize: FONT_SIZES.md,
    color: COLORS.text,
    textAlign: 'center',
    marginBottom: SPACING.lg,
  },
  errorList: {
    backgroundColor: COLORS.surface,
    padding: SPACING.md,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.border,
    marginBottom: SPACING.lg,
  },
  errorItem: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.error,
    marginBottom: SPACING.xs,
  },
  configHelpText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 20,
  },
});
