// Supabase Configuration
export const SUPABASE_CONFIG = {
  url: 'YOUR_SUPABASE_URL', // Replace with your Supabase URL
  anonKey: 'YOUR_SUPABASE_ANON_KEY', // Replace with your Supabase anon key
};

// ZEGOCLOUD Configuration
export const ZEGO_CONFIG = {
  appID: 0, // Replace with your ZEGOCLOUD App ID
  appSign: 'YOUR_ZEGO_APP_SIGN', // Replace with your ZEGOCLOUD App Sign
};

// App Configuration
export const APP_CONFIG = {
  name: 'VibeOk',
  version: '1.0.0',
  roomCodeLength: 8,
  maxParticipants: 10,
};

// Colors
export const COLORS = {
  primary: '#6366f1',
  primaryDark: '#4f46e5',
  secondary: '#10b981',
  background: '#f8fafc',
  surface: '#ffffff',
  text: '#1f2937',
  textSecondary: '#6b7280',
  error: '#ef4444',
  warning: '#f59e0b',
  success: '#10b981',
  border: '#e5e7eb',
};

// Spacing
export const SPACING = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

// Font Sizes
export const FONT_SIZES = {
  xs: 12,
  sm: 14,
  md: 16,
  lg: 18,
  xl: 20,
  xxl: 24,
  xxxl: 32,
};
