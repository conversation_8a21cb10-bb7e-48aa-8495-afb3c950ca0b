// Configuration Template for VibeOk
// Copy this file to src/constants/config.ts and fill in your actual values

// Supabase Configuration
export const SUPABASE_CONFIG = {
  url: process.env.EXPO_PUBLIC_SUPABASE_URL || 'https://your-project-id.supabase.co',
  anonKey: process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY || 'your-supabase-anon-key-here',
};

// ZEGOCLOUD Configuration
export const ZEGO_CONFIG = {
  appID: parseInt(process.env.EXPO_PUBLIC_ZEGO_APP_ID || '0'),
  appSign: process.env.EXPO_PUBLIC_ZEGO_APP_SIGN || 'your-zego-app-sign-here',
};

// App Configuration
export const APP_CONFIG = {
  name: 'VibeOk',
  version: '1.0.0',
  roomCodeLength: 8,
  maxParticipants: 10,
};

// Colors
export const COLORS = {
  primary: '#6366f1',
  primaryDark: '#4f46e5',
  secondary: '#10b981',
  background: '#f8fafc',
  surface: '#ffffff',
  text: '#1f2937',
  textSecondary: '#6b7280',
  error: '#ef4444',
  warning: '#f59e0b',
  success: '#10b981',
  border: '#e5e7eb',
};

// Spacing
export const SPACING = {
  xs: 4,
  sm: 8,
  md: 16,
  lg: 24,
  xl: 32,
  xxl: 48,
};

// Font Sizes
export const FONT_SIZES = {
  xs: 12,
  sm: 14,
  md: 16,
  lg: 18,
  xl: 20,
  xxl: 24,
  xxxl: 32,
};

/*
SETUP INSTRUCTIONS:

1. SUPABASE SETUP:
   - Create a new project at https://supabase.com/
   - Go to Settings > API
   - Copy your Project URL and anon/public key
   - Replace the values above

2. ZEGOCLOUD SETUP:
   - Create an account at https://www.zegocloud.com/
   - Create a new project
   - Go to project settings
   - Copy your App ID (number) and App Sign
   - Replace the values above

3. DATABASE SETUP:
   - In your Supabase project, go to SQL Editor
   - Run the SQL script from supabase-schema.sql
   - This will create all necessary tables and security policies

4. SAVE THIS FILE:
   - Save this file as src/constants/config.ts
   - Make sure to replace all placeholder values with your actual credentials
   - Do not commit your actual credentials to version control

5. TEST THE APP:
   - Run npm start
   - Test registration, login, and meeting creation
   - Verify video calls work properly
*/
