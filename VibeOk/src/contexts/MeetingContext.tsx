import React, { createContext, useContext, useState } from 'react';
import { MeetingContextType, Meeting } from '../types';
import { dbHelpers } from '../services/supabase';
import { generateRoomCode, formatErrorMessage } from '../utils/helpers';
import { useAuth } from './AuthContext';

const MeetingContext = createContext<MeetingContextType | undefined>(undefined);

export const useMeeting = () => {
  const context = useContext(MeetingContext);
  if (context === undefined) {
    throw new Error('useMeeting must be used within a MeetingProvider');
  }
  return context;
};

interface MeetingProviderProps {
  children: React.ReactNode;
}

export const MeetingProvider: React.FC<MeetingProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const [currentMeeting, setCurrentMeeting] = useState<Meeting | null>(null);
  const [meetings, setMeetings] = useState<Meeting[]>([]);
  const [loading, setLoading] = useState(false);

  const createMeeting = async (title?: string): Promise<string> => {
    if (!user) {
      throw new Error('User must be authenticated to create a meeting');
    }

    try {
      setLoading(true);
      const roomCode = generateRoomCode();
      
      // Check if room code already exists (very unlikely but possible)
      try {
        await dbHelpers.getMeetingByRoomCode(roomCode);
        // If we get here, room code exists, generate a new one
        return createMeeting(title);
      } catch (error) {
        // Room code doesn't exist, which is what we want
      }

      const meeting = await dbHelpers.createMeeting(user.id, roomCode, title);
      
      // Add creator as host participant
      await dbHelpers.addParticipant(meeting.id, user.id, true);
      
      setCurrentMeeting(meeting);
      
      // Refresh meetings list
      await getMeetingHistory();
      
      return roomCode;
    } catch (error) {
      throw new Error(formatErrorMessage(error));
    } finally {
      setLoading(false);
    }
  };

  const joinMeeting = async (roomCode: string): Promise<Meeting> => {
    if (!user) {
      throw new Error('User must be authenticated to join a meeting');
    }

    try {
      setLoading(true);
      const meeting = await dbHelpers.getMeetingByRoomCode(roomCode.toUpperCase());
      
      if (!meeting) {
        throw new Error('Meeting not found or has ended');
      }

      // Add user as participant
      try {
        await dbHelpers.addParticipant(meeting.id, user.id, false);
      } catch (error) {
        // User might already be a participant, which is fine
        console.log('User might already be a participant:', error);
      }
      
      setCurrentMeeting(meeting);
      return meeting;
    } catch (error) {
      throw new Error(formatErrorMessage(error));
    } finally {
      setLoading(false);
    }
  };

  const leaveMeeting = async (): Promise<void> => {
    if (!user || !currentMeeting) {
      return;
    }

    try {
      setLoading(true);
      
      // Remove user from participants
      await dbHelpers.removeParticipant(currentMeeting.id, user.id);
      
      // If user is the host, end the meeting
      if (currentMeeting.creator_id === user.id) {
        await dbHelpers.endMeeting(currentMeeting.id);
      }
      
      setCurrentMeeting(null);
      
      // Refresh meetings list
      await getMeetingHistory();
    } catch (error) {
      console.error('Error leaving meeting:', error);
      // Don't throw error here as user should be able to leave regardless
    } finally {
      setLoading(false);
    }
  };

  const getMeetingHistory = async (): Promise<void> => {
    if (!user) {
      return;
    }

    try {
      setLoading(true);
      const userMeetings = await dbHelpers.getUserMeetings(user.id);
      setMeetings(userMeetings || []);
    } catch (error) {
      console.error('Error fetching meeting history:', error);
      // Don't throw error here as this is not critical
    } finally {
      setLoading(false);
    }
  };

  const value: MeetingContextType = {
    currentMeeting,
    meetings,
    loading,
    createMeeting,
    joinMeeting,
    leaveMeeting,
    getMeetingHistory,
  };

  return (
    <MeetingContext.Provider value={value}>
      {children}
    </MeetingContext.Provider>
  );
};
