export interface User {
  id: string;
  email: string;
  full_name?: string;
  avatar_url?: string;
  created_at: string;
  updated_at: string;
}

export interface Meeting {
  id: string;
  room_code: string;
  title?: string;
  creator_id: string;
  created_at: string;
  updated_at: string;
  is_active: boolean;
  max_participants?: number;
}

export interface MeetingParticipant {
  id: string;
  meeting_id: string;
  user_id: string;
  joined_at: string;
  left_at?: string;
  is_host: boolean;
}

export interface AuthContextType {
  user: User | null;
  session: any;
  loading: boolean;
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, fullName: string) => Promise<void>;
  signOut: () => Promise<void>;
}

export interface MeetingContextType {
  currentMeeting: Meeting | null;
  meetings: Meeting[];
  loading: boolean;
  createMeeting: (title?: string) => Promise<string>;
  joinMeeting: (roomCode: string) => Promise<Meeting>;
  leaveMeeting: () => Promise<void>;
  getMeetingHistory: () => Promise<void>;
}

export type RootStackParamList = {
  Auth: undefined;
  Main: undefined;
  Login: undefined;
  Register: undefined;
  Home: undefined;
  CreateMeeting: undefined;
  JoinMeeting: undefined;
  MeetingRoom: { roomCode: string; meetingId: string };
  Profile: undefined;
  MeetingHistory: undefined;
};

export interface ZegoCallConfig {
  appID: number;
  appSign: string;
  userID: string;
  userName: string;
  callID: string;
}
