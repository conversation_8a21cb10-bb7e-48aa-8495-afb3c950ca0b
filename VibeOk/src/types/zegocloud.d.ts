declare module '@zegocloud/zego-uikit-prebuilt-video-conference-rn' {
  import { ComponentType } from 'react';

  export interface ZegoVideoConferenceConfig {
    onLeave?: () => void;
    onJoinConfirmation?: () => void;
    turnOnCameraWhenJoining?: boolean;
    turnOnMicrophoneWhenJoining?: boolean;
    useSpeakerWhenJoining?: boolean;
    audioVideoViewConfig?: {
      showMicrophoneStateOnView?: boolean;
      showCameraStateOnView?: boolean;
      showUserNameOnView?: boolean;
      useVideoViewAspectFill?: boolean;
    };
    bottomMenuBarConfig?: {
      maxCount?: number;
      buttons?: string[];
    };
    memberListConfig?: {
      showMicrophoneState?: boolean;
      showCameraState?: boolean;
    };
    layout?: {
      mode?: string;
      config?: {
        isSmallViewDraggable?: boolean;
        switchLargeOrSmallViewByClick?: boolean;
      };
    };
    branding?: {
      logoURL?: string;
    };
  }

  export interface ZegoUIKitPrebuiltVideoConferenceProps {
    appID: number;
    appSign: string;
    userID: string;
    userName: string;
    conferenceID: string;
    config?: ZegoVideoConferenceConfig;
  }

  const ZegoUIKitPrebuiltVideoConference: ComponentType<ZegoUIKitPrebuiltVideoConferenceProps>;
  export default ZegoUIKitPrebuiltVideoConference;
}
