declare module '@zegocloud/zego-uikit-prebuilt-call-rn' {
  import { ComponentType } from 'react';

  export interface ZegoCallConfig {
    onOnlySelfInRoom?: () => void;
    onHangUp?: () => void;
    turnOnCameraWhenJoining?: boolean;
    turnOnMicrophoneWhenJoining?: boolean;
    useSpeakerWhenJoining?: boolean;
    audioVideoViewConfig?: {
      showMicrophoneStateOnView?: boolean;
      showCameraStateOnView?: boolean;
      showUserNameOnView?: boolean;
      useVideoViewAspectFill?: boolean;
    };
    bottomMenuBarConfig?: {
      maxCount?: number;
      buttons?: string[];
    };
    memberListConfig?: {
      showMicrophoneState?: boolean;
      showCameraState?: boolean;
    };
    layout?: {
      mode?: string;
      config?: {
        isSmallViewDraggable?: boolean;
        switchLargeOrSmallViewByClick?: boolean;
      };
    };
  }

  export interface ZegoUIKitPrebuiltCallProps {
    appID: number;
    appSign: string;
    userID: string;
    userName: string;
    callID: string;
    config?: ZegoCallConfig;
  }

  export const ONE_ON_ONE_VIDEO_CALL_CONFIG: ZegoCallConfig;

  const ZegoUIKitPrebuiltCall: ComponentType<ZegoUIKitPrebuiltCallProps>;
  export default ZegoUIKitPrebuiltCall;
}
