import React from 'react';
import {
  View,
  Text,
  ActivityIndicator,
  StyleSheet,
  StatusBar,
} from 'react-native';
import { COLORS, FONT_SIZES, SPACING } from '../constants/config';

const LoadingScreen: React.FC = () => {
  console.log('⏳ LoadingScreen: Rendering');
  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={COLORS.primary} barStyle="light-content" />
      <View style={styles.content}>
        <Text style={styles.title}>VibeOk</Text>
        <Text style={styles.subtitle}>Connecting people through video</Text>
        <ActivityIndicator 
          size="large" 
          color={COLORS.primary} 
          style={styles.loader}
        />
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
    justifyContent: 'center',
    alignItems: 'center',
  },
  content: {
    alignItems: 'center',
    paddingHorizontal: SPACING.xl,
  },
  title: {
    fontSize: FONT_SIZES.xxxl,
    fontWeight: 'bold',
    color: COLORS.primary,
    marginBottom: SPACING.sm,
  },
  subtitle: {
    fontSize: FONT_SIZES.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.xl,
  },
  loader: {
    marginTop: SPACING.lg,
  },
});

export default LoadingScreen;
