import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  StatusBar,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';

import { useAuth } from '../../contexts/AuthContext';
import { useMeeting } from '../../contexts/MeetingContext';
import { RootStackParamList } from '../../types';
import { COLORS, FONT_SIZES, SPACING } from '../../constants/config';
import { dbHelpers } from '../../services/supabase';

type MeetingRoomScreenNavigationProp = StackNavigationProp<RootStackParamList, 'MeetingRoom'>;
type MeetingRoomScreenRouteProp = RouteProp<RootStackParamList, 'MeetingRoom'>;

interface Props {
  navigation: MeetingRoomScreenNavigationProp;
  route: MeetingRoomScreenRouteProp;
}

const MeetingRoomWebScreen: React.FC<Props> = ({ navigation, route }) => {
  const { user } = useAuth();
  const { leaveMeeting } = useMeeting();
  const { roomCode, meetingId } = route.params;
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    initializeMeeting();
  }, []);

  const initializeMeeting = async () => {
    try {
      if (user && meetingId) {
        await dbHelpers.addParticipant(meetingId, user.id, false);
      }
      setIsInitialized(true);
    } catch (error) {
      console.log('User might already be a participant:', error);
      setIsInitialized(true);
    }
  };

  const handleLeaveMeeting = async () => {
    try {
      await leaveMeeting();
      navigation.goBack();
    } catch (error) {
      console.error('Error leaving meeting:', error);
      navigation.goBack();
    }
  };

  if (!isInitialized || !user) {
    return (
      <View style={styles.loadingContainer}>
        <StatusBar backgroundColor={COLORS.primary} barStyle="light-content" />
        <Text style={styles.loadingText}>Joining meeting...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={COLORS.primary} barStyle="light-content" />
      
      <View style={styles.header}>
        <Text style={styles.roomCode}>Room: {roomCode}</Text>
        <Text style={styles.subtitle}>Web Preview Mode</Text>
      </View>

      <View style={styles.videoPlaceholder}>
        <Ionicons name="phone-portrait" size={80} color={COLORS.primary} />
        <Text style={styles.placeholderTitle}>📱 Mobile Experience Required</Text>
        <Text style={styles.placeholderText}>
          Video conferencing is optimized for mobile devices.{'\n\n'}
          To experience full video calling features:{'\n'}
          • Install Expo Go on your phone{'\n'}
          • Scan the QR code from the terminal{'\n'}
          • Join this meeting using code: {roomCode}
        </Text>
        
        <View style={styles.instructionsBox}>
          <Text style={styles.instructionsTitle}>How to join on mobile:</Text>
          <Text style={styles.instructionsText}>
            1. Open Expo Go app{'\n'}
            2. Scan QR code from terminal{'\n'}
            3. Login to VibeOk{'\n'}
            4. Join meeting with code: {roomCode}
          </Text>
        </View>
      </View>

      <View style={styles.controls}>
        <TouchableOpacity 
          style={styles.leaveButton} 
          onPress={handleLeaveMeeting}
        >
          <Ionicons name="arrow-back" size={24} color={COLORS.surface} />
          <Text style={styles.leaveButtonText}>Back to Home</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  loadingContainer: {
    flex: 1,
    backgroundColor: COLORS.background,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: FONT_SIZES.lg,
    color: COLORS.text,
    fontWeight: '500',
  },
  header: {
    backgroundColor: COLORS.primary,
    padding: SPACING.lg,
    alignItems: 'center',
  },
  roomCode: {
    fontSize: FONT_SIZES.xl,
    fontWeight: 'bold',
    color: COLORS.surface,
  },
  subtitle: {
    fontSize: FONT_SIZES.md,
    color: COLORS.surface,
    marginTop: SPACING.xs,
    opacity: 0.9,
  },
  videoPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: SPACING.xl,
  },
  placeholderTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: 'bold',
    color: COLORS.text,
    marginTop: SPACING.lg,
    marginBottom: SPACING.md,
    textAlign: 'center',
  },
  placeholderText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: SPACING.xl,
  },
  instructionsBox: {
    backgroundColor: COLORS.surface,
    padding: SPACING.lg,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.border,
    width: '100%',
    maxWidth: 400,
  },
  instructionsTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.sm,
  },
  instructionsText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.textSecondary,
    lineHeight: 20,
  },
  controls: {
    padding: SPACING.lg,
    backgroundColor: COLORS.surface,
    borderTopWidth: 1,
    borderTopColor: COLORS.border,
  },
  leaveButton: {
    backgroundColor: COLORS.primary,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.md,
    borderRadius: 8,
  },
  leaveButtonText: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.surface,
    marginLeft: SPACING.sm,
  },
});

export default MeetingRoomWebScreen;
