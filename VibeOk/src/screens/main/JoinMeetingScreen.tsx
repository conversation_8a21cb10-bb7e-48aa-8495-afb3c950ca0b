import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
  StatusBar,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { StackNavigationProp } from '@react-navigation/stack';

import { useMeeting } from '../../contexts/MeetingContext';
import { RootStackParamList } from '../../types';
import { COLORS, FONT_SIZES, SPACING, APP_CONFIG } from '../../constants/config';
import { validateRoomCode } from '../../utils/helpers';

type JoinMeetingScreenNavigationProp = StackNavigationProp<RootStackParamList, 'JoinMeeting'>;

interface Props {
  navigation: JoinMeetingScreenNavigationProp;
}

const JoinMeetingScreen: React.FC<Props> = ({ navigation }) => {
  const { joinMeeting, loading } = useMeeting();
  const [roomCode, setRoomCode] = useState('');
  const [error, setError] = useState('');

  const handleRoomCodeChange = (text: string) => {
    // Convert to uppercase and remove any non-alphanumeric characters
    const cleanedText = text.toUpperCase().replace(/[^A-Z0-9]/g, '');
    
    // Limit to the configured room code length
    const limitedText = cleanedText.slice(0, APP_CONFIG.roomCodeLength);
    
    setRoomCode(limitedText);
    
    // Clear error when user starts typing
    if (error) {
      setError('');
    }
  };

  const validateForm = (): boolean => {
    if (!roomCode.trim()) {
      setError('Please enter a meeting code');
      return false;
    }

    if (!validateRoomCode(roomCode)) {
      setError(`Meeting code must be ${APP_CONFIG.roomCodeLength} characters long`);
      return false;
    }

    return true;
  };

  const handleJoinMeeting = async () => {
    if (!validateForm()) {
      return;
    }

    try {
      const meeting = await joinMeeting(roomCode);
      navigation.navigate('MeetingRoom', { 
        roomCode: meeting.room_code, 
        meetingId: meeting.id 
      });
    } catch (error: any) {
      Alert.alert('Unable to Join Meeting', error.message || 'An unexpected error occurred');
    }
  };

  const formatRoomCode = (code: string): string => {
    // Add spaces every 2 characters for better readability
    return code.replace(/(.{2})/g, '$1 ').trim();
  };

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={COLORS.primary} barStyle="light-content" />
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <Ionicons name="enter" size={64} color={COLORS.primary} />
          <Text style={styles.title}>Join Meeting</Text>
          <Text style={styles.subtitle}>
            Enter the meeting code to join an existing meeting
          </Text>
        </View>

        <View style={styles.form}>
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Meeting Code</Text>
            <View style={styles.roomCodeInputContainer}>
              <TextInput
                style={[
                  styles.roomCodeInput,
                  error && styles.inputError,
                  { letterSpacing: 2 }
                ]}
                placeholder={`Enter ${APP_CONFIG.roomCodeLength}-digit code`}
                value={formatRoomCode(roomCode)}
                onChangeText={handleRoomCodeChange}
                autoCapitalize="characters"
                autoCorrect={false}
                maxLength={APP_CONFIG.roomCodeLength + Math.floor(APP_CONFIG.roomCodeLength / 2) - 1} // Account for spaces
                keyboardType="default"
                textAlign="center"
              />
            </View>
            {error ? (
              <Text style={styles.errorText}>{error}</Text>
            ) : (
              <Text style={styles.inputHint}>
                Ask the meeting host for the {APP_CONFIG.roomCodeLength}-character code
              </Text>
            )}
          </View>

          <View style={styles.infoBox}>
            <Ionicons name="information-circle" size={24} color={COLORS.primary} />
            <View style={styles.infoContent}>
              <Text style={styles.infoTitle}>How to join a meeting</Text>
              <Text style={styles.infoText}>
                • Get the meeting code from the host{'\n'}
                • Enter the code above{'\n'}
                • Tap "Join Meeting" to connect{'\n'}
                • Allow camera and microphone access
              </Text>
            </View>
          </View>

          <TouchableOpacity
            style={[
              styles.joinButton, 
              loading && styles.joinButtonDisabled,
              !roomCode.trim() && styles.joinButtonDisabled
            ]}
            onPress={handleJoinMeeting}
            disabled={loading || !roomCode.trim()}
          >
            <Ionicons 
              name="videocam" 
              size={24} 
              color={COLORS.surface} 
              style={styles.buttonIcon}
            />
            <Text style={styles.joinButtonText}>
              {loading ? 'Joining Meeting...' : 'Join Meeting'}
            </Text>
          </TouchableOpacity>

          <View style={styles.divider}>
            <View style={styles.dividerLine} />
            <Text style={styles.dividerText}>OR</Text>
            <View style={styles.dividerLine} />
          </View>

          <TouchableOpacity
            style={styles.createButton}
            onPress={() => navigation.navigate('CreateMeeting')}
          >
            <Ionicons 
              name="add-circle-outline" 
              size={24} 
              color={COLORS.primary} 
              style={styles.buttonIcon}
            />
            <Text style={styles.createButtonText}>Create New Meeting</Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
  },
  header: {
    alignItems: 'center',
    marginBottom: SPACING.xl,
  },
  title: {
    fontSize: FONT_SIZES.xxl,
    fontWeight: 'bold',
    color: COLORS.text,
    marginTop: SPACING.md,
    marginBottom: SPACING.sm,
  },
  subtitle: {
    fontSize: FONT_SIZES.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  form: {
    flex: 1,
  },
  inputContainer: {
    marginBottom: SPACING.xl,
  },
  label: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.md,
    textAlign: 'center',
  },
  roomCodeInputContainer: {
    alignItems: 'center',
  },
  roomCodeInput: {
    borderWidth: 2,
    borderColor: COLORS.border,
    borderRadius: 12,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
    fontSize: FONT_SIZES.xxl,
    fontWeight: 'bold',
    backgroundColor: COLORS.surface,
    width: '100%',
    color: COLORS.primary,
  },
  inputError: {
    borderColor: COLORS.error,
  },
  errorText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.error,
    marginTop: SPACING.sm,
    textAlign: 'center',
  },
  inputHint: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.textSecondary,
    marginTop: SPACING.sm,
    textAlign: 'center',
  },
  infoBox: {
    flexDirection: 'row',
    backgroundColor: COLORS.surface,
    padding: SPACING.md,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.border,
    marginBottom: SPACING.xl,
  },
  infoContent: {
    flex: 1,
    marginLeft: SPACING.md,
  },
  infoTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.sm,
  },
  infoText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.textSecondary,
    lineHeight: 20,
  },
  joinButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 8,
    paddingVertical: SPACING.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: SPACING.lg,
  },
  joinButtonDisabled: {
    backgroundColor: COLORS.textSecondary,
  },
  buttonIcon: {
    marginRight: SPACING.sm,
  },
  joinButtonText: {
    fontSize: FONT_SIZES.lg,
    fontWeight: '600',
    color: COLORS.surface,
  },
  divider: {
    flexDirection: 'row',
    alignItems: 'center',
    marginVertical: SPACING.lg,
  },
  dividerLine: {
    flex: 1,
    height: 1,
    backgroundColor: COLORS.border,
  },
  dividerText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.textSecondary,
    marginHorizontal: SPACING.md,
    fontWeight: '500',
  },
  createButton: {
    backgroundColor: COLORS.surface,
    borderWidth: 2,
    borderColor: COLORS.primary,
    borderRadius: 8,
    paddingVertical: SPACING.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  createButtonText: {
    fontSize: FONT_SIZES.lg,
    fontWeight: '600',
    color: COLORS.primary,
  },
});

export default JoinMeetingScreen;
