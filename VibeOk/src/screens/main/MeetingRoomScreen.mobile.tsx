import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  BackHandler,
  StatusBar,
} from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import ZegoUIKitPrebuiltCall, { ONE_ON_ONE_VIDEO_CALL_CONFIG } from '@zegocloud/zego-uikit-prebuilt-call-rn';

import { useAuth } from '../../contexts/AuthContext';
import { useMeeting } from '../../contexts/MeetingContext';
import { RootStackParamList } from '../../types';
import { COLORS, ZEGO_CONFIG } from '../../constants/config';
import { dbHelpers } from '../../services/supabase';

type MeetingRoomScreenNavigationProp = StackNavigationProp<RootStackParamList, 'MeetingRoom'>;
type MeetingRoomScreenRouteProp = RouteProp<RootStackParamList, 'MeetingRoom'>;

interface Props {
  navigation: MeetingRoomScreenNavigationProp;
  route: MeetingRoomScreenRouteProp;
}

const MeetingRoomScreen: React.FC<Props> = ({ navigation, route }) => {
  const { user } = useAuth();
  const { leaveMeeting } = useMeeting();
  const { roomCode, meetingId } = route.params;
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    // Handle hardware back button on Android
    const backHandler = BackHandler.addEventListener('hardwareBackPress', handleLeaveMeeting);
    
    // Initialize meeting participant tracking
    initializeMeeting();

    return () => {
      backHandler.remove();
    };
  }, []);

  const initializeMeeting = async () => {
    try {
      if (user && meetingId) {
        // Add user as participant if not already added
        await dbHelpers.addParticipant(meetingId, user.id, false);
      }
      setIsInitialized(true);
    } catch (error) {
      console.log('User might already be a participant:', error);
      setIsInitialized(true);
    }
  };

  const handleLeaveMeeting = (): boolean => {
    Alert.alert(
      'Leave Meeting',
      'Are you sure you want to leave this meeting?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Leave',
          style: 'destructive',
          onPress: async () => {
            try {
              await leaveMeeting();
              navigation.goBack();
            } catch (error) {
              console.error('Error leaving meeting:', error);
              navigation.goBack();
            }
          },
        },
      ]
    );
    return true; // Prevent default back behavior
  };

  if (!isInitialized || !user) {
    return (
      <View style={styles.loadingContainer}>
        <StatusBar backgroundColor={COLORS.primary} barStyle="light-content" />
        <Text style={styles.loadingText}>Joining meeting...</Text>
      </View>
    );
  }

  // Check if ZEGO configuration is properly set
  if (!ZEGO_CONFIG.appID || ZEGO_CONFIG.appID === 0) {
    return (
      <View style={styles.errorContainer}>
        <StatusBar backgroundColor={COLORS.error} barStyle="light-content" />
        <Text style={styles.errorTitle}>Configuration Error</Text>
        <Text style={styles.errorText}>
          ZEGOCLOUD configuration is missing. Please check your app configuration.
        </Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={COLORS.primary} barStyle="light-content" />
      <ZegoUIKitPrebuiltCall
        appID={ZEGO_CONFIG.appID}
        appSign={ZEGO_CONFIG.appSign}
        userID={user.id}
        userName={user.full_name || user.email}
        callID={roomCode}
        config={{
          ...ONE_ON_ONE_VIDEO_CALL_CONFIG,
          onOnlySelfInRoom: () => {
            // When user is alone in the room
            console.log('Only self in room');
          },
          onHangUp: () => {
            // When user hangs up
            handleLeaveMeeting();
          },
          turnOnCameraWhenJoining: true,
          turnOnMicrophoneWhenJoining: true,
          useSpeakerWhenJoining: true,
          audioVideoViewConfig: {
            showMicrophoneStateOnView: true,
            showCameraStateOnView: true,
            showUserNameOnView: true,
            useVideoViewAspectFill: false,
          },
          bottomMenuBarConfig: {
            maxCount: 5,
            buttons: [
              'toggleCameraButton',
              'toggleMicrophoneButton',
              'hangUpButton',
              'switchAudioOutputButton',
              'switchCameraButton',
            ],
          },
          memberListConfig: {
            showMicrophoneState: true,
            showCameraState: true,
          },
          layout: {
            mode: 'pictureInPicture',
            config: {
              isSmallViewDraggable: true,
              switchLargeOrSmallViewByClick: true,
            },
          },
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.text,
  },
  loadingContainer: {
    flex: 1,
    backgroundColor: COLORS.background,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 18,
    color: COLORS.text,
    fontWeight: '500',
  },
  errorContainer: {
    flex: 1,
    backgroundColor: COLORS.background,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 32,
  },
  errorTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: COLORS.error,
    marginBottom: 16,
    textAlign: 'center',
  },
  errorText: {
    fontSize: 16,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
  },
});

export default MeetingRoomScreen;
