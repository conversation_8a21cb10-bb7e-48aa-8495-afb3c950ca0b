import React, { useState } from 'react';
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
  ScrollView,
  StatusBar,
  Share,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { StackNavigationProp } from '@react-navigation/stack';

import { useMeeting } from '../../contexts/MeetingContext';
import { RootStackParamList } from '../../types';
import { COLORS, FONT_SIZES, SPACING } from '../../constants/config';

type CreateMeetingScreenNavigationProp = StackNavigationProp<RootStackParamList, 'CreateMeeting'>;

interface Props {
  navigation: CreateMeetingScreenNavigationProp;
}

const CreateMeetingScreen: React.FC<Props> = ({ navigation }) => {
  const { createMeeting, loading } = useMeeting();
  const [title, setTitle] = useState('');
  const [roomCode, setRoomCode] = useState<string | null>(null);
  const [meetingCreated, setMeetingCreated] = useState(false);

  const handleCreateMeeting = async () => {
    try {
      const generatedRoomCode = await createMeeting(title.trim() || undefined);
      setRoomCode(generatedRoomCode);
      setMeetingCreated(true);
    } catch (error: any) {
      Alert.alert('Error', error.message || 'An unexpected error occurred');
    }
  };

  const handleJoinMeeting = () => {
    if (roomCode) {
      navigation.navigate('MeetingRoom', { 
        roomCode, 
        meetingId: roomCode // We'll need to pass the actual meeting ID
      });
    }
  };

  const handleShareMeeting = async () => {
    if (roomCode) {
      try {
        await Share.share({
          message: `Join my VibeOk meeting!\n\nMeeting Code: ${roomCode}\n\nDownload VibeOk to join: [App Store/Play Store Link]`,
          title: 'Join my VibeOk meeting',
        });
      } catch (error) {
        console.error('Error sharing meeting:', error);
      }
    }
  };

  const handleCreateAnother = () => {
    setRoomCode(null);
    setMeetingCreated(false);
    setTitle('');
  };

  if (meetingCreated && roomCode) {
    return (
      <View style={styles.container}>
        <StatusBar backgroundColor={COLORS.primary} barStyle="light-content" />
        <ScrollView contentContainerStyle={styles.scrollContent}>
          <View style={styles.successContainer}>
            <View style={styles.successIcon}>
              <Ionicons name="checkmark-circle" size={80} color={COLORS.success} />
            </View>
            
            <Text style={styles.successTitle}>Meeting Created!</Text>
            <Text style={styles.successSubtitle}>
              Your meeting room is ready. Share the code with others to invite them.
            </Text>

            <View style={styles.roomCodeContainer}>
              <Text style={styles.roomCodeLabel}>Meeting Code</Text>
              <View style={styles.roomCodeBox}>
                <Text style={styles.roomCodeText}>{roomCode}</Text>
              </View>
              <Text style={styles.roomCodeHint}>
                Others can join using this code
              </Text>
            </View>

            <View style={styles.actionButtons}>
              <TouchableOpacity
                style={[styles.actionButton, styles.primaryButton]}
                onPress={handleJoinMeeting}
              >
                <Ionicons name="videocam" size={24} color={COLORS.surface} />
                <Text style={styles.primaryButtonText}>Join Meeting</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.actionButton, styles.secondaryButton]}
                onPress={handleShareMeeting}
              >
                <Ionicons name="share" size={24} color={COLORS.primary} />
                <Text style={styles.secondaryButtonText}>Share Code</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={[styles.actionButton, styles.tertiaryButton]}
                onPress={handleCreateAnother}
              >
                <Ionicons name="add" size={24} color={COLORS.textSecondary} />
                <Text style={styles.tertiaryButtonText}>Create Another</Text>
              </TouchableOpacity>
            </View>
          </View>
        </ScrollView>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={COLORS.primary} barStyle="light-content" />
      <ScrollView contentContainerStyle={styles.scrollContent}>
        <View style={styles.header}>
          <Ionicons name="videocam" size={64} color={COLORS.primary} />
          <Text style={styles.title}>Create New Meeting</Text>
          <Text style={styles.subtitle}>
            Start a video meeting and invite others to join
          </Text>
        </View>

        <View style={styles.form}>
          <View style={styles.inputContainer}>
            <Text style={styles.label}>Meeting Title (Optional)</Text>
            <TextInput
              style={styles.input}
              placeholder="e.g., Team Standup, Family Call"
              value={title}
              onChangeText={setTitle}
              maxLength={50}
              autoCapitalize="words"
            />
            <Text style={styles.inputHint}>
              Give your meeting a name to help identify it later
            </Text>
          </View>

          <View style={styles.infoBox}>
            <Ionicons name="information-circle" size={24} color={COLORS.primary} />
            <View style={styles.infoContent}>
              <Text style={styles.infoTitle}>What happens next?</Text>
              <Text style={styles.infoText}>
                • A unique meeting code will be generated{'\n'}
                • You can share this code with others{'\n'}
                • The meeting starts when you join{'\n'}
                • Up to 10 participants can join
              </Text>
            </View>
          </View>

          <TouchableOpacity
            style={[styles.createButton, loading && styles.createButtonDisabled]}
            onPress={handleCreateMeeting}
            disabled={loading}
          >
            <Ionicons 
              name="add-circle" 
              size={24} 
              color={COLORS.surface} 
              style={styles.buttonIcon}
            />
            <Text style={styles.createButtonText}>
              {loading ? 'Creating Meeting...' : 'Create Meeting'}
            </Text>
          </TouchableOpacity>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
  },
  header: {
    alignItems: 'center',
    marginBottom: SPACING.xl,
  },
  title: {
    fontSize: FONT_SIZES.xxl,
    fontWeight: 'bold',
    color: COLORS.text,
    marginTop: SPACING.md,
    marginBottom: SPACING.sm,
  },
  subtitle: {
    fontSize: FONT_SIZES.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
  },
  form: {
    flex: 1,
  },
  inputContainer: {
    marginBottom: SPACING.xl,
  },
  label: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.sm,
  },
  input: {
    borderWidth: 1,
    borderColor: COLORS.border,
    borderRadius: 8,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.md,
    fontSize: FONT_SIZES.md,
    backgroundColor: COLORS.surface,
  },
  inputHint: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.textSecondary,
    marginTop: SPACING.sm,
  },
  infoBox: {
    flexDirection: 'row',
    backgroundColor: COLORS.surface,
    padding: SPACING.md,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.border,
    marginBottom: SPACING.xl,
  },
  infoContent: {
    flex: 1,
    marginLeft: SPACING.md,
  },
  infoTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.sm,
  },
  infoText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.textSecondary,
    lineHeight: 20,
  },
  createButton: {
    backgroundColor: COLORS.primary,
    borderRadius: 8,
    paddingVertical: SPACING.md,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 'auto',
  },
  createButtonDisabled: {
    backgroundColor: COLORS.textSecondary,
  },
  buttonIcon: {
    marginRight: SPACING.sm,
  },
  createButtonText: {
    fontSize: FONT_SIZES.lg,
    fontWeight: '600',
    color: COLORS.surface,
  },
  // Success screen styles
  successContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  successIcon: {
    marginBottom: SPACING.lg,
  },
  successTitle: {
    fontSize: FONT_SIZES.xxl,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: SPACING.sm,
  },
  successSubtitle: {
    fontSize: FONT_SIZES.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginBottom: SPACING.xl,
    lineHeight: 22,
  },
  roomCodeContainer: {
    alignItems: 'center',
    marginBottom: SPACING.xl,
  },
  roomCodeLabel: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.sm,
  },
  roomCodeBox: {
    backgroundColor: COLORS.surface,
    borderWidth: 2,
    borderColor: COLORS.primary,
    borderRadius: 8,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    marginBottom: SPACING.sm,
  },
  roomCodeText: {
    fontSize: FONT_SIZES.xxxl,
    fontWeight: 'bold',
    color: COLORS.primary,
    letterSpacing: 2,
  },
  roomCodeHint: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.textSecondary,
  },
  actionButtons: {
    width: '100%',
    gap: SPACING.md,
  },
  actionButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.md,
    borderRadius: 8,
  },
  primaryButton: {
    backgroundColor: COLORS.primary,
  },
  secondaryButton: {
    backgroundColor: COLORS.surface,
    borderWidth: 2,
    borderColor: COLORS.primary,
  },
  tertiaryButton: {
    backgroundColor: COLORS.surface,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  primaryButtonText: {
    fontSize: FONT_SIZES.lg,
    fontWeight: '600',
    color: COLORS.surface,
    marginLeft: SPACING.sm,
  },
  secondaryButtonText: {
    fontSize: FONT_SIZES.lg,
    fontWeight: '600',
    color: COLORS.primary,
    marginLeft: SPACING.sm,
  },
  tertiaryButtonText: {
    fontSize: FONT_SIZES.lg,
    fontWeight: '600',
    color: COLORS.textSecondary,
    marginLeft: SPACING.sm,
  },
});

export default CreateMeetingScreen;
