import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  BackHandler,
  StatusBar,
  TouchableOpacity,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';

import { useAuth } from '../../contexts/AuthContext';
import { useMeeting } from '../../contexts/MeetingContext';
import { RootStackParamList } from '../../types';
import { COLORS, FONT_SIZES, SPACING } from '../../constants/config';
import { dbHelpers } from '../../services/supabase';

type MeetingRoomScreenNavigationProp = StackNavigationProp<RootStackParamList, 'MeetingRoom'>;
type MeetingRoomScreenRouteProp = RouteProp<RootStackParamList, 'MeetingRoom'>;

interface Props {
  navigation: MeetingRoomScreenNavigationProp;
  route: MeetingRoomScreenRouteProp;
}

const MeetingRoomScreen: React.FC<Props> = ({ navigation, route }) => {
  const { user } = useAuth();
  const { leaveMeeting } = useMeeting();
  const { roomCode, meetingId } = route.params;
  const [isInitialized, setIsInitialized] = useState(false);
  const [participants, setParticipants] = useState<any[]>([]);

  useEffect(() => {
    // Handle hardware back button on Android
    const backHandler = BackHandler.addEventListener('hardwareBackPress', handleLeaveMeeting);
    
    // Initialize meeting participant tracking
    initializeMeeting();

    return () => {
      backHandler.remove();
    };
  }, []);

  const initializeMeeting = async () => {
    try {
      if (user && meetingId) {
        // Add user as participant if not already added
        await dbHelpers.addParticipant(meetingId, user.id, false);
        
        // Load participants
        const participantsList = await dbHelpers.getMeetingParticipants(meetingId);
        setParticipants(participantsList || []);
      }
      setIsInitialized(true);
    } catch (error) {
      console.log('User might already be a participant:', error);
      setIsInitialized(true);
    }
  };

  const handleLeaveMeeting = (): boolean => {
    Alert.alert(
      'Leave Meeting',
      'Are you sure you want to leave this meeting?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Leave',
          style: 'destructive',
          onPress: async () => {
            try {
              await leaveMeeting();
              navigation.goBack();
            } catch (error) {
              console.error('Error leaving meeting:', error);
              navigation.goBack();
            }
          },
        },
      ]
    );
    return true; // Prevent default back behavior
  };

  if (!isInitialized || !user) {
    return (
      <View style={styles.loadingContainer}>
        <StatusBar backgroundColor={COLORS.primary} barStyle="light-content" />
        <Text style={styles.loadingText}>Joining meeting...</Text>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={COLORS.primary} barStyle="light-content" />
      
      <View style={styles.header}>
        <Text style={styles.roomCode}>Room: {roomCode}</Text>
        <Text style={styles.participantCount}>
          {participants.length} participant{participants.length !== 1 ? 's' : ''}
        </Text>
      </View>

      <View style={styles.videoPlaceholder}>
        <Ionicons name="videocam-off" size={80} color={COLORS.textSecondary} />
        <Text style={styles.placeholderTitle}>Video Calling</Text>
        <Text style={styles.placeholderText}>
          ZEGOCLOUD integration will be available on mobile devices.
          This is a placeholder for web testing.
        </Text>
      </View>

      <View style={styles.participantsList}>
        <Text style={styles.participantsTitle}>Participants:</Text>
        {participants.map((participant, index) => (
          <View key={participant.id} style={styles.participantItem}>
            <Ionicons name="person" size={20} color={COLORS.primary} />
            <Text style={styles.participantName}>
              {participant.profiles?.full_name || participant.profiles?.email || 'Unknown User'}
              {participant.is_host && ' (Host)'}
            </Text>
          </View>
        ))}
      </View>

      <View style={styles.controls}>
        <TouchableOpacity style={styles.controlButton}>
          <Ionicons name="mic-off" size={24} color={COLORS.surface} />
          <Text style={styles.controlText}>Mute</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.controlButton}>
          <Ionicons name="videocam-off" size={24} color={COLORS.surface} />
          <Text style={styles.controlText}>Camera</Text>
        </TouchableOpacity>

        <TouchableOpacity 
          style={[styles.controlButton, styles.leaveButton]} 
          onPress={handleLeaveMeeting}
        >
          <Ionicons name="call" size={24} color={COLORS.surface} />
          <Text style={styles.controlText}>Leave</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.text,
  },
  loadingContainer: {
    flex: 1,
    backgroundColor: COLORS.background,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: FONT_SIZES.lg,
    color: COLORS.text,
    fontWeight: '500',
  },
  header: {
    backgroundColor: COLORS.primary,
    padding: SPACING.md,
    alignItems: 'center',
  },
  roomCode: {
    fontSize: FONT_SIZES.lg,
    fontWeight: 'bold',
    color: COLORS.surface,
  },
  participantCount: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.surface,
    marginTop: SPACING.xs,
  },
  videoPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.background,
    margin: SPACING.md,
    borderRadius: 8,
  },
  placeholderTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: 'bold',
    color: COLORS.text,
    marginTop: SPACING.md,
  },
  placeholderText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
    marginTop: SPACING.sm,
    paddingHorizontal: SPACING.lg,
    lineHeight: 22,
  },
  participantsList: {
    backgroundColor: COLORS.surface,
    margin: SPACING.md,
    padding: SPACING.md,
    borderRadius: 8,
  },
  participantsTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.sm,
  },
  participantItem: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: SPACING.xs,
  },
  participantName: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.text,
    marginLeft: SPACING.sm,
  },
  controls: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    backgroundColor: COLORS.text,
    paddingVertical: SPACING.lg,
    paddingHorizontal: SPACING.md,
  },
  controlButton: {
    backgroundColor: COLORS.textSecondary,
    borderRadius: 50,
    width: 60,
    height: 60,
    justifyContent: 'center',
    alignItems: 'center',
  },
  leaveButton: {
    backgroundColor: COLORS.error,
  },
  controlText: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.surface,
    marginTop: SPACING.xs,
  },
});

export default MeetingRoomScreen;
