import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Alert,
  BackHandler,
  StatusBar,
  Platform,
} from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import ZegoUIKitPrebuiltVideoConference from '@zegocloud/zego-uikit-prebuilt-video-conference-rn';

import { useAuth } from '../../contexts/AuthContext';
import { useMeeting } from '../../contexts/MeetingContext';
import { RootStackParamList } from '../../types';
import { COLORS, FONT_SIZES, SPACING, ZEGO_CONFIG } from '../../constants/config';
import { dbHelpers } from '../../services/supabase';

type MeetingRoomScreenNavigationProp = StackNavigationProp<RootStackParamList, 'MeetingRoom'>;
type MeetingRoomScreenRouteProp = RouteProp<RootStackParamList, 'MeetingRoom'>;

interface Props {
  navigation: MeetingRoomScreenNavigationProp;
  route: MeetingRoomScreenRouteProp;
}

const MeetingRoomScreen: React.FC<Props> = ({ navigation, route }) => {
  const { user } = useAuth();
  const { leaveMeeting } = useMeeting();
  const { roomCode, meetingId } = route.params;
  const [isInitialized, setIsInitialized] = useState(false);

  useEffect(() => {
    // Handle hardware back button on Android
    const backHandler = BackHandler.addEventListener('hardwareBackPress', handleBackPress);

    // Initialize meeting participant tracking
    initializeMeeting();

    return () => {
      backHandler.remove();
    };
  }, []);

  const initializeMeeting = async () => {
    try {
      if (user && meetingId) {
        // Add user as participant if not already added
        await dbHelpers.addParticipant(meetingId, user.id, false);
      }
      setIsInitialized(true);
    } catch (error) {
      console.log('User might already be a participant:', error);
      setIsInitialized(true);
    }
  };

  const handleLeaveMeeting = async () => {
    try {
      await leaveMeeting();
      navigation.goBack();
    } catch (error) {
      console.error('Error leaving meeting:', error);
      navigation.goBack();
    }
  };

  const handleBackPress = (): boolean => {
    Alert.alert(
      'Leave Meeting',
      'Are you sure you want to leave this meeting?',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Leave',
          style: 'destructive',
          onPress: handleLeaveMeeting,
        },
      ]
    );
    return true; // Prevent default back behavior
  };

  if (!isInitialized || !user) {
    return (
      <View style={styles.loadingContainer}>
        <StatusBar backgroundColor={COLORS.primary} barStyle="light-content" />
        <Text style={styles.loadingText}>Joining meeting...</Text>
      </View>
    );
  }

  // Check if ZEGO configuration is properly set
  if (!ZEGO_CONFIG.appID || ZEGO_CONFIG.appID === 0) {
    return (
      <View style={styles.errorContainer}>
        <StatusBar backgroundColor={COLORS.error} barStyle="light-content" />
        <Text style={styles.errorTitle}>Configuration Error</Text>
        <Text style={styles.errorText}>
          ZEGOCLOUD configuration is missing. Please check your app configuration.
        </Text>
      </View>
    );
  }

  // For web platform, show a placeholder since ZEGOCLOUD has limited web support
  if (Platform.OS === 'web') {
    return (
      <View style={styles.container}>
        <StatusBar backgroundColor={COLORS.primary} barStyle="light-content" />

        <View style={styles.header}>
          <Text style={styles.roomCode}>Room: {roomCode}</Text>
          <Text style={styles.participantCount}>Web Preview Mode</Text>
        </View>

        <View style={styles.videoPlaceholder}>
          <Text style={styles.placeholderTitle}>📱 Mobile Only Feature</Text>
          <Text style={styles.placeholderText}>
            Video conferencing is available on mobile devices.{'\n'}
            Please test on iOS or Android using Expo Go app.
          </Text>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={COLORS.primary} barStyle="light-content" />
      <ZegoUIKitPrebuiltVideoConference
        appID={ZEGO_CONFIG.appID}
        appSign={ZEGO_CONFIG.appSign}
        userID={user.id}
        userName={user.full_name || user.email}
        conferenceID={roomCode}
        config={{
          onLeave: handleLeaveMeeting,
          turnOnCameraWhenJoining: true,
          turnOnMicrophoneWhenJoining: true,
          useSpeakerWhenJoining: true,
          audioVideoViewConfig: {
            showMicrophoneStateOnView: true,
            showCameraStateOnView: true,
            showUserNameOnView: true,
            useVideoViewAspectFill: false,
          },
          bottomMenuBarConfig: {
            maxCount: 5,
            buttons: [
              'toggleCameraButton',
              'toggleMicrophoneButton',
              'hangUpButton',
              'switchAudioOutputButton',
              'switchCameraButton',
            ],
          },
          memberListConfig: {
            showMicrophoneState: true,
            showCameraState: true,
          },
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.text,
  },
  loadingContainer: {
    flex: 1,
    backgroundColor: COLORS.background,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: FONT_SIZES.lg,
    color: COLORS.text,
    fontWeight: '500',
  },
  errorContainer: {
    flex: 1,
    backgroundColor: COLORS.background,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: SPACING.xl,
  },
  errorTitle: {
    fontSize: FONT_SIZES.xxl,
    fontWeight: 'bold',
    color: COLORS.error,
    marginBottom: SPACING.md,
    textAlign: 'center',
  },
  errorText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
  },
  header: {
    backgroundColor: COLORS.primary,
    padding: SPACING.md,
    alignItems: 'center',
  },
  roomCode: {
    fontSize: FONT_SIZES.lg,
    fontWeight: 'bold',
    color: COLORS.surface,
  },
  participantCount: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.surface,
    marginTop: SPACING.xs,
  },
  videoPlaceholder: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: COLORS.background,
    margin: SPACING.md,
    borderRadius: 8,
    padding: SPACING.lg,
  },
  placeholderTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: SPACING.md,
    textAlign: 'center',
  },
  placeholderText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 24,
  },

});

export default MeetingRoomScreen;
