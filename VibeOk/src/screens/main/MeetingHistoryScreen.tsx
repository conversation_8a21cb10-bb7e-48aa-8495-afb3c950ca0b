import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  StatusBar,
  RefreshControl,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { StackNavigationProp } from '@react-navigation/stack';

import { useMeeting } from '../../contexts/MeetingContext';
import { RootStackParamList, Meeting } from '../../types';
import { COLORS, FONT_SIZES, SPACING } from '../../constants/config';
import { formatDate, formatDuration } from '../../utils/helpers';

type MeetingHistoryScreenNavigationProp = StackNavigationProp<RootStackParamList, 'MeetingHistory'>;

interface Props {
  navigation: MeetingHistoryScreenNavigationProp;
}

const MeetingHistoryScreen: React.FC<Props> = ({ navigation }) => {
  const { meetings, getMeetingHistory, loading } = useMeeting();
  const [refreshing, setRefreshing] = useState(false);

  useEffect(() => {
    getMeetingHistory();
  }, []);

  const handleRefresh = async () => {
    setRefreshing(true);
    try {
      await getMeetingHistory();
    } catch (error) {
      console.error('Error refreshing meetings:', error);
    } finally {
      setRefreshing(false);
    }
  };

  const handleMeetingPress = (meeting: Meeting) => {
    if (meeting.is_active) {
      Alert.alert(
        'Join Active Meeting',
        `Do you want to join the meeting "${meeting.title || meeting.room_code}"?`,
        [
          {
            text: 'Cancel',
            style: 'cancel',
          },
          {
            text: 'Join',
            onPress: () => {
              navigation.navigate('MeetingRoom', {
                roomCode: meeting.room_code,
                meetingId: meeting.id,
              });
            },
          },
        ]
      );
    } else {
      Alert.alert(
        'Meeting Details',
        `Meeting: ${meeting.title || meeting.room_code}\nCode: ${meeting.room_code}\nCreated: ${formatDate(meeting.created_at)}\nStatus: ${meeting.is_active ? 'Active' : 'Ended'}`,
        [{ text: 'OK' }]
      );
    }
  };

  const renderMeetingItem = (meeting: Meeting) => (
    <TouchableOpacity
      key={meeting.id}
      style={styles.meetingItem}
      onPress={() => handleMeetingPress(meeting)}
    >
      <View style={styles.meetingIcon}>
        <Ionicons
          name="videocam"
          size={24}
          color={meeting.is_active ? COLORS.success : COLORS.textSecondary}
        />
      </View>

      <View style={styles.meetingContent}>
        <View style={styles.meetingHeader}>
          <Text style={styles.meetingTitle}>
            {meeting.title || `Meeting ${meeting.room_code}`}
          </Text>
          <View style={[
            styles.statusBadge,
            { backgroundColor: meeting.is_active ? COLORS.success : COLORS.textSecondary }
          ]}>
            <Text style={styles.statusText}>
              {meeting.is_active ? 'Active' : 'Ended'}
            </Text>
          </View>
        </View>

        <View style={styles.meetingDetails}>
          <View style={styles.detailRow}>
            <Ionicons name="key" size={16} color={COLORS.textSecondary} />
            <Text style={styles.detailText}>Code: {meeting.room_code}</Text>
          </View>

          <View style={styles.detailRow}>
            <Ionicons name="calendar" size={16} color={COLORS.textSecondary} />
            <Text style={styles.detailText}>{formatDate(meeting.created_at)}</Text>
          </View>

          {!meeting.is_active && (
            <View style={styles.detailRow}>
              <Ionicons name="time" size={16} color={COLORS.textSecondary} />
              <Text style={styles.detailText}>
                Duration: {formatDuration(meeting.created_at, meeting.updated_at)}
              </Text>
            </View>
          )}
        </View>
      </View>

      <View style={styles.meetingActions}>
        <Ionicons name="chevron-forward" size={20} color={COLORS.textSecondary} />
      </View>
    </TouchableOpacity>
  );

  const activeMeetings = meetings.filter(meeting => meeting.is_active);
  const pastMeetings = meetings.filter(meeting => !meeting.is_active);

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={COLORS.primary} barStyle="light-content" />
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[COLORS.primary]}
            tintColor={COLORS.primary}
          />
        }
      >
        {meetings.length === 0 && !loading ? (
          <View style={styles.emptyState}>
            <Ionicons name="videocam-off" size={64} color={COLORS.textSecondary} />
            <Text style={styles.emptyTitle}>No Meetings Yet</Text>
            <Text style={styles.emptySubtitle}>
              Your meeting history will appear here once you create or join meetings.
            </Text>
            <TouchableOpacity
              style={styles.createMeetingButton}
              onPress={() => navigation.navigate('CreateMeeting')}
            >
              <Ionicons name="add" size={24} color={COLORS.surface} />
              <Text style={styles.createMeetingText}>Create Your First Meeting</Text>
            </TouchableOpacity>
          </View>
        ) : (
          <>
            {/* Active Meetings */}
            {activeMeetings.length > 0 && (
              <View style={styles.section}>
                <View style={styles.sectionHeader}>
                  <Text style={styles.sectionTitle}>Active Meetings</Text>
                  <View style={styles.activeBadge}>
                    <Text style={styles.activeBadgeText}>{activeMeetings.length}</Text>
                  </View>
                </View>
                <View style={styles.meetingsList}>
                  {activeMeetings.map(renderMeetingItem)}
                </View>
              </View>
            )}

            {/* Past Meetings */}
            {pastMeetings.length > 0 && (
              <View style={styles.section}>
                <Text style={styles.sectionTitle}>Past Meetings</Text>
                <View style={styles.meetingsList}>
                  {pastMeetings.map(renderMeetingItem)}
                </View>
              </View>
            )}

            {/* Quick Actions */}
            <View style={styles.quickActions}>
              <TouchableOpacity
                style={styles.quickActionButton}
                onPress={() => navigation.navigate('CreateMeeting')}
              >
                <Ionicons name="add-circle" size={24} color={COLORS.primary} />
                <Text style={styles.quickActionText}>Create Meeting</Text>
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.quickActionButton}
                onPress={() => navigation.navigate('JoinMeeting')}
              >
                <Ionicons name="enter" size={24} color={COLORS.primary} />
                <Text style={styles.quickActionText}>Join Meeting</Text>
              </TouchableOpacity>
            </View>
          </>
        )}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  scrollContent: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
  },
  section: {
    marginBottom: SPACING.xl,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: '600',
    color: COLORS.text,
  },
  activeBadge: {
    backgroundColor: COLORS.success,
    borderRadius: 12,
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
    marginLeft: SPACING.sm,
  },
  activeBadgeText: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.surface,
    fontWeight: 'bold',
  },
  meetingsList: {
    gap: SPACING.sm,
  },
  meetingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.surface,
    padding: SPACING.md,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  meetingIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: COLORS.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  meetingContent: {
    flex: 1,
  },
  meetingHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: SPACING.sm,
  },
  meetingTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.text,
    flex: 1,
    marginRight: SPACING.sm,
  },
  statusBadge: {
    borderRadius: 4,
    paddingHorizontal: SPACING.sm,
    paddingVertical: SPACING.xs,
  },
  statusText: {
    fontSize: FONT_SIZES.xs,
    color: COLORS.surface,
    fontWeight: '500',
  },
  meetingDetails: {
    gap: SPACING.xs,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  detailText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.textSecondary,
    marginLeft: SPACING.sm,
  },
  meetingActions: {
    marginLeft: SPACING.sm,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: SPACING.xxl,
  },
  emptyTitle: {
    fontSize: FONT_SIZES.xl,
    fontWeight: 'bold',
    color: COLORS.text,
    marginTop: SPACING.lg,
    marginBottom: SPACING.sm,
  },
  emptySubtitle: {
    fontSize: FONT_SIZES.md,
    color: COLORS.textSecondary,
    textAlign: 'center',
    lineHeight: 22,
    marginBottom: SPACING.xl,
  },
  createMeetingButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.primary,
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.md,
    borderRadius: 8,
  },
  createMeetingText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.surface,
    fontWeight: '600',
    marginLeft: SPACING.sm,
  },
  quickActions: {
    flexDirection: 'row',
    gap: SPACING.md,
    marginTop: SPACING.lg,
  },
  quickActionButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: COLORS.surface,
    borderWidth: 2,
    borderColor: COLORS.primary,
    paddingVertical: SPACING.md,
    borderRadius: 8,
  },
  quickActionText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.primary,
    fontWeight: '600',
    marginLeft: SPACING.sm,
  },
});

export default MeetingHistoryScreen;
