import React, { useEffect } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  StatusBar,
  Alert,
} from 'react-native';
import { Ionicons } from '@expo/vector-icons';
import { StackNavigationProp } from '@react-navigation/stack';

import { useAuth } from '../../contexts/AuthContext';
import { useMeeting } from '../../contexts/MeetingContext';
import { RootStackParamList } from '../../types';
import { COLORS, FONT_SIZES, SPACING } from '../../constants/config';
import { formatDate } from '../../utils/helpers';

type HomeScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Home'>;

interface Props {
  navigation: HomeScreenNavigationProp;
}

const HomeScreen: React.FC<Props> = ({ navigation }) => {
  const { user } = useAuth();
  const { meetings, getMeetingHistory, loading } = useMeeting();

  useEffect(() => {
    getMeetingHistory();
  }, []);

  const handleCreateMeeting = () => {
    navigation.navigate('CreateMeeting');
  };

  const handleJoinMeeting = () => {
    navigation.navigate('JoinMeeting');
  };

  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return 'Good morning';
    if (hour < 18) return 'Good afternoon';
    return 'Good evening';
  };

  const recentMeetings = meetings.slice(0, 3);

  return (
    <View style={styles.container}>
      <StatusBar backgroundColor={COLORS.primary} barStyle="light-content" />
      <ScrollView contentContainerStyle={styles.scrollContent}>
        {/* Welcome Section */}
        <View style={styles.welcomeSection}>
          <Text style={styles.greeting}>
            {getGreeting()}, {user?.full_name?.split(' ')[0] || 'User'}!
          </Text>
          <Text style={styles.subtitle}>Ready to connect with others?</Text>
        </View>

        {/* Quick Actions */}
        <View style={styles.actionsSection}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          
          <View style={styles.actionButtons}>
            <TouchableOpacity
              style={[styles.actionButton, styles.primaryAction]}
              onPress={handleCreateMeeting}
            >
              <Ionicons name="videocam" size={32} color={COLORS.surface} />
              <Text style={styles.primaryActionText}>Start Meeting</Text>
              <Text style={styles.actionSubtext}>Create a new meeting room</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={[styles.actionButton, styles.secondaryAction]}
              onPress={handleJoinMeeting}
            >
              <Ionicons name="enter" size={32} color={COLORS.primary} />
              <Text style={styles.secondaryActionText}>Join Meeting</Text>
              <Text style={styles.actionSubtext}>Enter a meeting code</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Recent Meetings */}
        {recentMeetings.length > 0 && (
          <View style={styles.recentSection}>
            <View style={styles.sectionHeader}>
              <Text style={styles.sectionTitle}>Recent Meetings</Text>
              <TouchableOpacity onPress={() => navigation.navigate('MeetingHistory')}>
                <Text style={styles.viewAllText}>View All</Text>
              </TouchableOpacity>
            </View>

            <View style={styles.meetingsList}>
              {recentMeetings.map((meeting) => (
                <View key={meeting.id} style={styles.meetingItem}>
                  <View style={styles.meetingIcon}>
                    <Ionicons 
                      name="videocam" 
                      size={20} 
                      color={meeting.is_active ? COLORS.success : COLORS.textSecondary} 
                    />
                  </View>
                  <View style={styles.meetingInfo}>
                    <Text style={styles.meetingTitle}>
                      {meeting.title || `Meeting ${meeting.room_code}`}
                    </Text>
                    <Text style={styles.meetingDetails}>
                      Code: {meeting.room_code} • {formatDate(meeting.created_at)}
                    </Text>
                    <Text style={[
                      styles.meetingStatus,
                      { color: meeting.is_active ? COLORS.success : COLORS.textSecondary }
                    ]}>
                      {meeting.is_active ? 'Active' : 'Ended'}
                    </Text>
                  </View>
                </View>
              ))}
            </View>
          </View>
        )}

        {/* Features Section */}
        <View style={styles.featuresSection}>
          <Text style={styles.sectionTitle}>Features</Text>
          
          <View style={styles.featuresList}>
            <View style={styles.featureItem}>
              <Ionicons name="shield-checkmark" size={24} color={COLORS.success} />
              <Text style={styles.featureText}>Secure & Private</Text>
            </View>
            
            <View style={styles.featureItem}>
              <Ionicons name="people" size={24} color={COLORS.primary} />
              <Text style={styles.featureText}>Multi-participant</Text>
            </View>
            
            <View style={styles.featureItem}>
              <Ionicons name="phone-portrait" size={24} color={COLORS.secondary} />
              <Text style={styles.featureText}>Mobile Optimized</Text>
            </View>
            
            <View style={styles.featureItem}>
              <Ionicons name="flash" size={24} color={COLORS.warning} />
              <Text style={styles.featureText}>High Quality</Text>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: COLORS.background,
  },
  scrollContent: {
    paddingHorizontal: SPACING.lg,
    paddingVertical: SPACING.lg,
  },
  welcomeSection: {
    marginBottom: SPACING.xl,
  },
  greeting: {
    fontSize: FONT_SIZES.xxl,
    fontWeight: 'bold',
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  subtitle: {
    fontSize: FONT_SIZES.md,
    color: COLORS.textSecondary,
  },
  actionsSection: {
    marginBottom: SPACING.xl,
  },
  sectionTitle: {
    fontSize: FONT_SIZES.lg,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.md,
  },
  actionButtons: {
    gap: SPACING.md,
  },
  actionButton: {
    padding: SPACING.lg,
    borderRadius: 12,
    alignItems: 'center',
  },
  primaryAction: {
    backgroundColor: COLORS.primary,
  },
  secondaryAction: {
    backgroundColor: COLORS.surface,
    borderWidth: 2,
    borderColor: COLORS.primary,
  },
  primaryActionText: {
    fontSize: FONT_SIZES.lg,
    fontWeight: '600',
    color: COLORS.surface,
    marginTop: SPACING.sm,
  },
  secondaryActionText: {
    fontSize: FONT_SIZES.lg,
    fontWeight: '600',
    color: COLORS.primary,
    marginTop: SPACING.sm,
  },
  actionSubtext: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.textSecondary,
    marginTop: SPACING.xs,
  },
  recentSection: {
    marginBottom: SPACING.xl,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: SPACING.md,
  },
  viewAllText: {
    fontSize: FONT_SIZES.md,
    color: COLORS.primary,
    fontWeight: '600',
  },
  meetingsList: {
    gap: SPACING.sm,
  },
  meetingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.surface,
    padding: SPACING.md,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.border,
  },
  meetingIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: COLORS.background,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: SPACING.md,
  },
  meetingInfo: {
    flex: 1,
  },
  meetingTitle: {
    fontSize: FONT_SIZES.md,
    fontWeight: '600',
    color: COLORS.text,
    marginBottom: SPACING.xs,
  },
  meetingDetails: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.textSecondary,
    marginBottom: SPACING.xs,
  },
  meetingStatus: {
    fontSize: FONT_SIZES.sm,
    fontWeight: '500',
  },
  featuresSection: {
    marginBottom: SPACING.xl,
  },
  featuresList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: SPACING.md,
  },
  featureItem: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: COLORS.surface,
    paddingHorizontal: SPACING.md,
    paddingVertical: SPACING.sm,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: COLORS.border,
    minWidth: '45%',
  },
  featureText: {
    fontSize: FONT_SIZES.sm,
    color: COLORS.text,
    marginLeft: SPACING.sm,
    fontWeight: '500',
  },
});

export default HomeScreen;
