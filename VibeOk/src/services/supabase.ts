import { createClient } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { SUPABASE_CONFIG } from '../constants/config';
import 'react-native-url-polyfill/auto';

console.log('🗄️ Supabase: Initializing with config:', {
  url: SUPABASE_CONFIG.url,
  hasKey: !!SUPABASE_CONFIG.anonKey,
});

const supabaseUrl = SUPABASE_CONFIG.url;
const supabaseAnonKey = SUPABASE_CONFIG.anonKey;

let supabase: any;

try {
  supabase = createClient(supabaseUrl, supabaseAnonKey, {
    auth: {
      storage: AsyncStorage,
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: false,
    },
  });
  console.log('🗄️ Supabase: Client created successfully');
} catch (error) {
  console.error('🗄️ Supabase: Error creating client:', error);
  throw error;
}

export { supabase };

// Database helper functions
export const dbHelpers = {
  // User operations
  async createUserProfile(userId: string, email: string, fullName: string) {
    const { data, error } = await supabase
      .from('profiles')
      .insert([
        {
          id: userId,
          email,
          full_name: fullName,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ])
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async getUserProfile(userId: string) {
    const { data, error } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (error) throw error;
    return data;
  },

  async updateUserProfile(userId: string, updates: any) {
    const { data, error } = await supabase
      .from('profiles')
      .update({ ...updates, updated_at: new Date().toISOString() })
      .eq('id', userId)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Meeting operations
  async createMeeting(creatorId: string, roomCode: string, title?: string) {
    const { data, error } = await supabase
      .from('meetings')
      .insert([
        {
          room_code: roomCode,
          title: title || `Meeting ${roomCode}`,
          creator_id: creatorId,
          is_active: true,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
      ])
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async getMeetingByRoomCode(roomCode: string) {
    const { data, error } = await supabase
      .from('meetings')
      .select('*')
      .eq('room_code', roomCode)
      .eq('is_active', true)
      .single();

    if (error) throw error;
    return data;
  },

  async getUserMeetings(userId: string) {
    const { data, error } = await supabase
      .from('meetings')
      .select('*')
      .eq('creator_id', userId)
      .order('created_at', { ascending: false });

    if (error) throw error;
    return data;
  },

  async endMeeting(meetingId: string) {
    const { data, error } = await supabase
      .from('meetings')
      .update({ 
        is_active: false, 
        updated_at: new Date().toISOString() 
      })
      .eq('id', meetingId)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  // Meeting participant operations
  async addParticipant(meetingId: string, userId: string, isHost: boolean = false) {
    const { data, error } = await supabase
      .from('meeting_participants')
      .insert([
        {
          meeting_id: meetingId,
          user_id: userId,
          is_host: isHost,
          joined_at: new Date().toISOString(),
        },
      ])
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async removeParticipant(meetingId: string, userId: string) {
    const { data, error } = await supabase
      .from('meeting_participants')
      .update({ left_at: new Date().toISOString() })
      .eq('meeting_id', meetingId)
      .eq('user_id', userId)
      .is('left_at', null)
      .select()
      .single();

    if (error) throw error;
    return data;
  },

  async getMeetingParticipants(meetingId: string) {
    const { data, error } = await supabase
      .from('meeting_participants')
      .select(`
        *,
        profiles (
          id,
          email,
          full_name,
          avatar_url
        )
      `)
      .eq('meeting_id', meetingId)
      .is('left_at', null);

    if (error) throw error;
    return data;
  },
};
